'use client';

import { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { jwtDecode } from 'jwt-decode';
import axios from 'axios';

type User = {
    id?: string;
    address?: string;
    phone?: string;
    // Add other user properties as needed
};

type AuthContextType = {
    user: User | null;
    loading: boolean;
    authError: string | null;
    login: (address: string) => Promise<any>;
    logout: () => void;
    updatePhone: (phone: string) => Promise<any>;
    fetchUserProfile: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
    const [user, setUser] = useState<User | null>(null);
    const [loading, setLoading] = useState(true);
    const [authError, setAuthError] = useState<string | null>(null);
    const router = useRouter();

    // Configure axios defaults
    useEffect(() => {
        axios.defaults.baseURL = process.env.NEXT_PUBLIC_API_URL || 'https://api.crefy-phygitals-v2.crefy.xyz/api/v1';

        axios.interceptors.request.use((config) => {
            if (typeof window !== 'undefined') {
                const token = localStorage.getItem('token');
                if (token) {
                    config.headers.Authorization = `Bearer ${token}`;
                }
            }

            // 👇 Append headers to bypass browser-like requests

            return config;
        });

    }, []);

    const login = async (address: string) => {
        try {
            setLoading(true);
            setAuthError(null);

            const { data } = await axios.post('/auth/login', { address });
            if (typeof window !== 'undefined') {
                localStorage.setItem('token', data.token);
            }
            setUser(data.user);
            return data;
        } catch (error: any) {
            const message = error.response?.data?.error || 'Login failed';
            setAuthError(message);
            throw new Error(message);
        } finally {
            setLoading(false);
        }
    };

    const logout = () => {
        if (typeof window !== 'undefined') {
            localStorage.removeItem('token');
        }
        setUser(null);
        setAuthError(null);
        router.push('/');
    };

    const updatePhone = async (phone: string) => {
        try {
            setLoading(true);
            const { data } = await axios.put('/users/phone', { phone });
            setUser((prev: any) => ({ ...prev, phone: data.phone }));
            return data;
        } catch (error: any) {
            const message = error.response?.data?.error || 'Update failed';
            setAuthError(message);
            throw new Error(message);
        } finally {
            setLoading(false);
        }
    };

    const fetchUserProfile = async () => {
        try {
            if (typeof window === 'undefined') return;

            const token = localStorage.getItem('token');
            if (!token) return;

            const decoded: any = jwtDecode(token);
            const { data } = await axios.get(`/users/${decoded.sub}`);
            setUser(data);
        } catch (error) {
            console.error('Profile fetch error:', error);
            logout();
        }
    };

    // Initialize auth state
    useEffect(() => {
        const checkAuth = async () => {
            if (typeof window === 'undefined') {
                setLoading(false);
                return;
            }

            const token = localStorage.getItem('token');
            if (!token) {
                setLoading(false);
                return;
            }
        };

        checkAuth();
    }, []);

    return (
        <AuthContext.Provider
            value={{
                user,
                loading,
                authError,
                login,
                logout,
                updatePhone,
                fetchUserProfile,
            }}
        >
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};