export interface Product {
    _id: string;
    name: string;
    description: string;
    price: number;
    originalPrice?: number;
    type: string;
    rating: number;
    reviews: number;
    features: string[];
    image: string;
}

// Wallet Authentication Types
export interface WalletLoginRequest {
    address: string;
    socialType?: string;
}

export interface UserProfile {
    _id: string;
    walletAddress: string;
    socialType?: string;
    createdAt: string;
    lastLogin: string;
}

export interface Developer {
    id: string;
    name: string;
    email: string;
    isActive: boolean;
    createdAt: string;
}

export interface ErrorResponse {
    success: boolean;
    message: string;
}

export interface WalletAuthResponse {
    success: boolean;
    user?: UserProfile;
    token?: string;
    message?: string;
}