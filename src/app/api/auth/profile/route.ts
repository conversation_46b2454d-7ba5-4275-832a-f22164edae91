import { NextRequest, NextResponse } from 'next/server';
import { UserProfile, ErrorResponse } from '@/types';
import { userService, extractWalletFromAuth } from '../store';

export async function GET(request: NextRequest) {
    try {
        // Extract wallet address from Authorization header
        const authHeader = request.headers.get('Authorization');
        const walletAddress = extractWalletFromAuth(authHeader);
        
        if (!walletAddress) {
            const errorResponse: ErrorResponse = {
                success: false,
                message: 'Unauthorized - invalid or missing wallet address'
            };
            return NextResponse.json(errorResponse, { status: 401 });
        }

        // Find user by wallet address
        const user = userService.findByWalletAddress(walletAddress);
        
        if (!user) {
            const errorResponse: ErrorResponse = {
                success: false,
                message: 'User not found'
            };
            return NextResponse.json(errorResponse, { status: 404 });
        }

        // Return user profile (excluding sensitive data)
        const userProfile: UserProfile = {
            _id: user._id,
            walletAddress: user.walletAddress,
            socialType: user.socialType,
            createdAt: user.createdAt,
            lastLogin: user.lastLogin
        };

        return NextResponse.json(userProfile, { status: 200 });

    } catch (error) {
        console.error('Profile fetch error:', error);
        
        const errorResponse: ErrorResponse = {
            success: false,
            message: 'Internal server error'
        };
        return NextResponse.json(errorResponse, { status: 500 });
    }
}

// Handle unsupported methods
export async function POST() {
    const errorResponse: ErrorResponse = {
        success: false,
        message: 'Method not allowed'
    };
    return NextResponse.json(errorResponse, { status: 405 });
}

export async function PUT() {
    const errorResponse: ErrorResponse = {
        success: false,
        message: 'Method not allowed'
    };
    return NextResponse.json(errorResponse, { status: 405 });
}

export async function DELETE() {
    const errorResponse: ErrorResponse = {
        success: false,
        message: 'Method not allowed'
    };
    return NextResponse.json(errorResponse, { status: 405 });
}
