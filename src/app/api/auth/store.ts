import { UserProfile } from '@/types';

// In-memory store for demo purposes
// In production, replace with actual database (MongoDB, PostgreSQL, etc.)
export const userStore: UserProfile[] = [];

// Helper functions for user management
export const userService = {
    // Find user by wallet address
    findByWalletAddress: (address: string): UserProfile | undefined => {
        return userStore.find(u => u.walletAddress.toLowerCase() === address.toLowerCase());
    },

    // Create new user
    createUser: (address: string, socialType?: string): UserProfile => {
        const user: UserProfile = {
            _id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            walletAddress: address,
            socialType: socialType || 'wallet',
            createdAt: new Date().toISOString(),
            lastLogin: new Date().toISOString()
        };
        userStore.push(user);
        return user;
    },

    // Update user's last login
    updateLastLogin: (user: UserProfile): UserProfile => {
        user.lastLogin = new Date().toISOString();
        return user;
    },

    // Update user's social type
    updateSocialType: (user: UserProfile, socialType: string): UserProfile => {
        user.socialType = socialType;
        return user;
    }
};

// Helper function to validate wallet address
export function isValidWalletAddress(address: string): boolean {
    // Basic Ethereum address validation
    return /^0x[a-fA-F0-9]{40}$/.test(address);
}

// Helper function to generate a simple JWT-like token
export function generateToken(walletAddress: string): string {
    // In production, use a proper JWT library with secret key
    const payload = {
        walletAddress,
        timestamp: Date.now(),
    };
    return Buffer.from(JSON.stringify(payload)).toString('base64');
}

// Helper function to decode token and extract wallet address
export function decodeToken(token: string): string | null {
    try {
        const payload = JSON.parse(Buffer.from(token, 'base64').toString());
        return payload.walletAddress;
    } catch (error) {
        return null;
    }
}

// Helper function to extract wallet address from Authorization header
export function extractWalletFromAuth(authHeader: string | null): string | null {
    if (!authHeader) return null;
    
    // Handle "Bearer <token>" format
    if (authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        return decodeToken(token);
    }
    
    // Handle direct wallet address
    if (authHeader.startsWith('0x') && authHeader.length === 42) {
        return authHeader;
    }
    
    return null;
}
