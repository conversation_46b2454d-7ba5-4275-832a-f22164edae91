import { NextRequest, NextResponse } from 'next/server';
import { WalletLoginRequest, ErrorResponse } from '@/types';
import { userService, isValidWalletAddress, generateToken } from '../store';

export async function POST(request: NextRequest) {
    try {
        const body: WalletLoginRequest = await request.json();
        
        // Validate request body
        if (!body.address) {
            const errorResponse: ErrorResponse = {
                success: false,
                message: 'Wallet address is required'
            };
            return NextResponse.json(errorResponse, { status: 400 });
        }

        // Validate wallet address format
        if (!isValidWalletAddress(body.address)) {
            const errorResponse: ErrorResponse = {
                success: false,
                message: 'Invalid wallet address format'
            };
            return NextResponse.json(errorResponse, { status: 400 });
        }

        // Check if user exists
        let user = userService.findByWalletAddress(body.address);

        if (!user) {
            // Create new user (register)
            user = userService.createUser(body.address, body.socialType);
        } else {
            // Update last login for existing user
            userService.updateLastLogin(user);
            if (body.socialType) {
                userService.updateSocialType(user, body.socialType);
            }
        }

        // Generate token
        const token = generateToken(user.walletAddress);

        // Return success response
        return NextResponse.json({
            _id: user._id,
            walletAddress: user.walletAddress,
            socialType: user.socialType,
            createdAt: user.createdAt,
            lastLogin: user.lastLogin,
            token
        }, { status: 200 });

    } catch (error) {
        console.error('Login error:', error);
        
        const errorResponse: ErrorResponse = {
            success: false,
            message: 'Internal server error'
        };
        return NextResponse.json(errorResponse, { status: 500 });
    }
}

// Handle unsupported methods
export async function GET() {
    const errorResponse: ErrorResponse = {
        success: false,
        message: 'Method not allowed'
    };
    return NextResponse.json(errorResponse, { status: 405 });
}

export async function PUT() {
    const errorResponse: ErrorResponse = {
        success: false,
        message: 'Method not allowed'
    };
    return NextResponse.json(errorResponse, { status: 405 });
}

export async function DELETE() {
    const errorResponse: ErrorResponse = {
        success: false,
        message: 'Method not allowed'
    };
    return NextResponse.json(errorResponse, { status: 405 });
}
