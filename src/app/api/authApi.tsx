import axios from 'axios';
import { WalletLoginRequest, UserProfile, ErrorResponse } from '@/types';

// Use local API endpoints instead of external API
const API_BASE_URL = '/api';

// For storing auth token and wallet address
const AUTH_TOKEN_KEY = 'authToken';
const WALLET_ADDRESS_KEY = 'walletAddress';

export const authService = {
    // Login with wallet address
    login: async (address: string, socialType?: string) => {
        try {
            const requestData: WalletLoginRequest = {
                address,
                socialType
            };

            const response = await axios.post(`${API_BASE_URL}/auth/login`, requestData, {
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            // Store token and wallet address in localStorage
            if (response.data.token) {
                localStorage.setItem(AUTH_TOKEN_KEY, response.data.token);
            }
            localStorage.setItem(WALLET_ADDRESS_KEY, address);

            return response.data;
        } catch (error: any) {
            console.error("Login error:", error);

            // Handle different error types
            if (error.response?.data?.message) {
                throw new Error(error.response.data.message);
            }
            throw error;
        }
    },

    // Get stored token
    getAccessToken: () => localStorage.getItem(AUTH_TOKEN_KEY),

    // Get stored wallet address
    getWalletAddress: () => localStorage.getItem(WALLET_ADDRESS_KEY),

    // Logout
    logout: () => {
        localStorage.removeItem(AUTH_TOKEN_KEY);
        localStorage.removeItem(WALLET_ADDRESS_KEY);
    },

    // Check authentication status
    isAuthenticated: () => !!localStorage.getItem(AUTH_TOKEN_KEY)
};