import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;

// For storing auth token and wallet address
const AUTH_TOKEN_KEY = 'authToken';
const WALLET_ADDRESS_KEY = 'walletAddress';

export const authService = {
    // Login with signed message
    login: async (address: string) => {
        try {
            const response = await axios.post(`${API_BASE_URL}/auth/login`, {
                address,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
            }
            });

            // Store in localStorage (or sessionStorage for more security)
            localStorage.setItem(AUTH_TOKEN_KEY, response.data.token);
            localStorage.setItem(WALLET_ADDRESS_KEY, address);

            return response.data;
        } catch (error) {
            console.error("Login error:", error);
            throw error;
        }
    },

    // Get stored token
    getAccessToken: () => localStorage.getItem(AUTH_TOKEN_KEY),

    // Get stored wallet address
    getWalletAddress: () => localStorage.getItem(WALLET_ADDRESS_KEY),

    // Logout
    logout: () => {
        localStorage.removeItem(AUTH_TOKEN_KEY);
        localStorage.removeItem(WALLET_ADDRESS_KEY);
    },

    // Check authentication status
    isAuthenticated: () => !!localStorage.getItem(AUTH_TOKEN_KEY)
};