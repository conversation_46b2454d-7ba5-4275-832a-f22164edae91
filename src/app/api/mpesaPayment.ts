import axios from 'axios';

const API_BASE_URL = 'https://mutually-factual-weevil.ngrok-free.app/api/swypt';

export const swyptApi = {
    // Get supported assets
    getSupportedAssets: async () => {
        const response = await axios.get(`${API_BASE_URL}/supported-assets`, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
            }
        });
        return response.data;
    },

    // Get quote for transaction
    // @ts-ignore
    getQuote: async (data: any) => {
        const response = await axios.post(`${API_BASE_URL}/quotes`, data, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
            }
        });
        return response.data;
    },

    // Initiate on-ramp transaction
    // @ts-ignore
    initiateOnRamp: async (data: any) => {
        const response = await axios.post(`${API_BASE_URL}/onramp`, data,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
                }
            }
        );
        return response.data;
    },

    // Get on-ramp transaction status
    getOnRampStatus: async (orderId: string) => {
        const response = await axios.get(`${API_BASE_URL}/onramp-status/${orderId}`,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
                }
            }
        );
        return response.data;
    },

    // Create on-ramp ticket
    // @ts-ignore
    createOnRampTicket: async (data: any) => {
        const response = await axios.post(`${API_BASE_URL}/onramp-ticket`, data,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
                }
            }
        );
        return response.data;
    },

    // Get transaction by order ID
    // @ts-ignore
    getTransaction: async (orderId: string) => {
        const response = await axios.get(`${API_BASE_URL}/transactions/${orderId}`,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
                }
            }
        );
        return response.data;
    },

    // Get all transactions for a user
    // @ts-ignore
    getUserTransactions: async (userAddress: string) => {
        const response = await axios.get(
            `${API_BASE_URL}/transactions/user/${userAddress}`,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
                }
            }
        );
        return response.data;
    },
};