import axios from 'axios';
import { getAuthToken } from '@/utils/getToken';
// Set the base URL for fetch
const API_ENDPOINT = process.env.NEXT_PUBLIC_API_URL;

export const getUser = async () => {
    const token = getAuthToken();
    const { data } = await axios.get(`${API_ENDPOINT}/user`, 
        {
            headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token || ''}`
        }
        }
    );
    return data;
};

// Product endpoints
export const getProducts = async () => {
    const token = getAuthToken();
    const { data } = await axios.get(`${API_ENDPOINT}/products`, 
        {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token || ''}`
            }
        }
    );
    console.log('data', data);
    return data;
};

export const seedProducts = async () => {
    const token = getAuthToken();
    const { data } = await axios.post(`${API_ENDPOINT}/products/seed`, 
        {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token || ''}`
            }
        }
    );
    return data;
};

// Cart endpoints
export const getCart = async () => {
    const token = getAuthToken();
    const { data } = await axios.get(`${API_ENDPOINT}/cart`, 
        {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token || ''}`
            }
        }
    );
    return data;
};

export const addToCart = async (productId: string, quantity: number = 1) => {
    const token = getAuthToken();
    const { data } = await axios.post(`${API_ENDPOINT}/cart/add`, { productId, quantity }, 
        {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token || ''}`
            }
        }
    );
    return data;
};

export const updateCartItem = async (productId: string, quantity: number) => {
    const token = getAuthToken();
    const { data } = await axios.put(`${API_ENDPOINT}/cart/update`, { productId, quantity }, 
        {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token || ''}`
            }
        }
    );
    return data;
};

export const removeFromCart = async (productId: string) => {
    const token = getAuthToken();
    const { data } = await axios.delete(`${API_ENDPOINT}/cart/remove`, {
        data: { productId },
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token || ''}`
        }
    });
    return data;
};


export const clearCart = async () => {
    const token = getAuthToken();
    const { data } = await axios.delete(`${API_ENDPOINT}/cart/clear`, {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token || ''}`
        }
    });
    return data;
};

// Order endpoints (updated for cart integration)
export const checkoutCart = async () => {
    const token = getAuthToken();
    const { data } = await axios.post(`${API_ENDPOINT}/cart/checkout`, {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token || ''}`
        }
    });
    return data;
};

// Legacy order endpoint (keep for backward compatibility)
export const createOrder = async (items: any) => {
    const token = getAuthToken();
    const { data } = await axios.post(`${API_ENDPOINT}/orders`, { items, 
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token || ''}`
        }
    });
    return data;
};

export const completePayment = async (orderId: string, mpesaCode: string) => {
    const token = getAuthToken();
    const { data } = await axios.post(`${API_ENDPOINT}/orders/complete`, { orderId, mpesaCode }, {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token || ''}`
        }
    });
    return data;
};

// Token endpoints
export const mintToken = async () => {
    const token = getAuthToken();
    const { data } = await axios.post(`${API_ENDPOINT}/tokens/mint`, {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token || ''}`
        }
    });
    return data;
};

export const getUserTokens = async () => {
    const token = getAuthToken();
    const { data } = await axios.get(`${API_ENDPOINT}/tokens`, {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token || ''}`
        }
    });
    return data;
};

export const redeemToken = async (tokenId: string, location: string) => {
    const token = getAuthToken();
    const { data } = await axios.post(`${API_ENDPOINT}/tokens/${tokenId}/redeem`, { location }, {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token || ''}`
        }
    });
    return data;
};

export const generateQRCode = async (tokenId: string) => {
    const token = getAuthToken();
    const { data } = await axios.get(`${API_ENDPOINT}/tokens/${tokenId}/qrcode`, {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token || ''}`
        }
    });
    return data.qrCode;
};