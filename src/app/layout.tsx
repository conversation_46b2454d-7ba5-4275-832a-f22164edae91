import type { Metada<PERSON> } from "next";
import { <PERSON>eist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { CrefyProvider, SocialLogins } from "@/component/crefy-connect";
import { AuthProvider } from "@/context/AuthContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};


const socialLogins: SocialLogins = {
  google: true,
  github: false,
  discord: false,
  telegram: false,
  twitter: false,
  wallet: false,
  email: true,
  phone: false,
}

const config = {
  socialLogins: socialLogins,
  crefyId: '**********',
  appearance: {
    primaryColor: '#000000',
    secondaryColor: '#000000',
  },
}


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <CrefyProvider config={config}>
          <AuthProvider>
            {children}
          </AuthProvider>
        </CrefyProvider>
      </body>
    </html>
  );
}
