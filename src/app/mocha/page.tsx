'use client';

import { useState, useEffect } from "react";
import { Coffee, ShoppingBag, Gift, Star, Plus, Minus, ShoppingCart, Menu, X, CheckCircle, AlertCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { getProducts, addToCart as addTo<PERSON>art<PERSON>pi, getCart, mintToken as mintToken<PERSON>pi, getUser } from "../api/api";
import { Product } from "@/types";
import Image from "next/image";

// Toast Component
const Toast = ({ message, type, onClose }: { message: string; type: 'success' | 'error'; onClose: () => void }) => {
    useEffect(() => {
        const timer = setTimeout(() => {
            onClose();
        }, 3000);

        return () => clearTimeout(timer);
    }, [onClose]);

    return (
        <div className={`fixed top-6 right-6 z-[60] transform transition-all duration-300 ease-in-out ${type === 'success'
            ? 'bg-gradient-to-r from-green-500 to-emerald-600'
            : 'bg-gradient-to-r from-red-500 to-rose-600'
            } text-white px-6 py-4 rounded-xl shadow-2xl flex items-center gap-3 max-w-sm animate-in slide-in-from-right-full`}>
            {type === 'success' ? (
                <CheckCircle className="w-6 h-6 flex-shrink-0" />
            ) : (
                <AlertCircle className="w-6 h-6 flex-shrink-0" />
            )}
            <span className="font-medium">{message}</span>
            <button
                onClick={onClose}
                className="ml-auto text-white/80 hover:text-white transition-colors"
            >
                <X className="w-4 h-4" />
            </button>
        </div>
    );
};

// Skeleton Loader Component
const ProductSkeleton = () => (
    <div className="group relative">
        <div className="bg-gradient-to-br from-amber-50/10 to-orange-100/5 backdrop-blur-sm border border-amber-500/20 rounded-2xl p-8 animate-pulse">
            {/* Badge Skeleton */}
            <div className="absolute -top-3 left-6">
                <div className="h-6 w-24 bg-amber-500/20 rounded-full"></div>
            </div>

            {/* Product Image Skeleton */}
            <div className="text-center mb-6">
                <div className="h-32 w-32 mx-auto bg-amber-500/20 rounded-full mb-4"></div>
                <div className="flex items-center justify-center mb-2">
                    <div className="flex gap-1">
                        {[...Array(5)].map((_, i) => (
                            <div key={i} className="h-4 w-4 bg-amber-500/20 rounded-full"></div>
                        ))}
                    </div>
                    <div className="ml-2 h-4 w-16 bg-amber-500/20 rounded-full"></div>
                </div>
            </div>

            {/* Product Info Skeleton */}
            <div className="text-center mb-6">
                <div className="h-8 w-3/4 mx-auto bg-amber-500/20 rounded-full mb-3"></div>
                <div className="h-4 w-full mx-auto bg-amber-500/20 rounded-full mb-2"></div>
                <div className="h-4 w-5/6 mx-auto bg-amber-500/20 rounded-full mb-2"></div>
                <div className="h-4 w-2/3 mx-auto bg-amber-500/20 rounded-full mb-4"></div>

                {/* Features Skeleton */}
                <div className="flex flex-wrap gap-2 justify-center mb-6">
                    <div className="h-6 w-16 bg-amber-500/20 rounded-full"></div>
                    <div className="h-6 w-20 bg-amber-500/20 rounded-full"></div>
                    <div className="h-6 w-14 bg-amber-500/20 rounded-full"></div>
                </div>

                {/* Price Skeleton */}
                <div className="flex items-center justify-center gap-3 mb-6">
                    <div className="h-10 w-24 bg-amber-500/20 rounded-full"></div>
                </div>
            </div>

            {/* Quantity & Add to Cart Skeleton */}
            <div className="space-y-4">
                <div className="flex items-center justify-center gap-4">
                    <div className="w-10 h-10 rounded-full bg-amber-500/20"></div>
                    <div className="h-10 w-10 bg-amber-500/20 rounded-full"></div>
                    <div className="w-10 h-10 rounded-full bg-amber-500/20"></div>
                </div>
                <div className="h-12 w-full bg-amber-500/20 rounded-xl"></div>
            </div>
        </div>
    </div>
);

export default function MochaPage() {
    const router = useRouter();
    const [quantities, setQuantities] = useState<Record<string, number>>({});
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const [cartItems, setCartItems] = useState(0);
    const [products, setProducts] = useState<Product[]>([]);
    const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null);
    const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});
    const [isMinting, setIsMinting] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    // @ts-ignore
    const [user, setUser] = useState<any>(null);

    useEffect(() => {
        const fetchUser = async () => {
            try {
                const user = await getUser();
                setUser(user);
            } catch (error) {
                console.error('Error fetching user:', error);
            }
        };
        fetchUser();
    }, []);

    const mintToken = async () => {
        setIsMinting(true);
        try {
            // @ts-ignore
            const response = await mintTokenApi();
            setToast({
                type: 'success',
                message: 'Token minted successfully!'
            });
            router.push('/mocha/free-coffee');
        } catch (error) {
            if (user?.coffee_tokens > 0) {
                setToast({
                    type: 'error',
                    message: 'You have already minted a coffee token'
                });
                router.push('/mocha/free-coffee');
                return;
            }
            console.error('Error minting token:', error);
        } finally {
            setIsMinting(false);
        }
    };

    const fetchProducts = async () => {
        setIsLoading(true);
        try {
            const response = await getProducts();
            const products = Array.isArray(response) ? response : [];
            setProducts(products);

            const initialQuantities = products.reduce((acc: Record<string, number>, product: Product) => {
                acc[product._id] = 1;
                return acc;
            }, {});

            setQuantities(initialQuantities);
        } catch (error) {
            console.error('Error fetching products:', error);
            setProducts([]);
            setToast({
                type: 'error',
                message: 'Failed to load products. Please try again.'
            });
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchProducts();
    }, []);

    const fetchCart = async () => {
        try {
            const cart = await getCart();
            setCartItems(cart?.items?.length || 0);
        } catch (error) {
            console.error('Error fetching cart:', error);
            setCartItems(0);
        }
    };

    useEffect(() => {
        fetchCart();
    }, []);

    const updateQuantity = (productId: string, change: number) => {
        setQuantities(prev => ({
            ...prev,
            [productId]: Math.max(1, (prev[productId] || 1) + change)
        }));
    };

    const addToCart = async (product: Product) => {
        const productId = product._id;
        const quantity = quantities[productId];

        setLoadingStates(prev => ({ ...prev, [productId]: true }));

        try {
            await addToCartApi(productId, quantity);
            setToast({
                type: 'success',
                message: `${quantity} ${product.name} added to cart successfully!`
            });
            fetchCart();
        } catch (error) {
            console.error('Error adding to cart:', error);
            setToast({
                type: 'error',
                message: 'Failed to add item to cart. Please try again.'
            });
        } finally {
            setLoadingStates(prev => ({ ...prev, [productId]: false }));
        }
    };

    const closeToast = () => {
        setToast(null);
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-900 via-amber-900 to-orange-900 relative overflow-hidden">
            {/* Toast Notification */}
            {toast && (
                <Toast
                    message={toast.message}
                    type={toast.type}
                    onClose={closeToast}
                />
            )}

            {/* Background Pattern */}
            <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=`60` height=`60` viewBox=`0 0 60 60` xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f59e0b' fill-opacity='0.03'%3E%3Cpath d='m36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>

            {/* Floating Coffee Beans */}
            <div className="absolute top-20 left-10 text-6xl opacity-10 animate-bounce">☕</div>
            <div className="absolute top-40 right-20 text-4xl opacity-10 animate-pulse">🫘</div>
            <div className="absolute bottom-32 left-20 text-5xl opacity-10 animate-bounce delay-1000">☕</div>
            <div className="absolute bottom-20 right-10 text-3xl opacity-10 animate-pulse delay-500">🫘</div>

            {/* Mobile Menu Overlay */}
            {mobileMenuOpen && (
                <div className="fixed inset-0 bg-black/80 z-50 backdrop-blur-sm">
                    <div className="absolute top-6 right-6">
                        <button
                            onClick={() => setMobileMenuOpen(false)}
                            className="text-amber-300 hover:text-amber-100 transition-colors"
                        >
                            <X className="w-8 h-8" />
                        </button>
                    </div>
                    <div className="flex flex-col items-center justify-center h-full gap-8 text-2xl">
                        <button onClick={() => router.push('/')} className="text-amber-300 hover:text-amber-100 transition-colors">Home</button>
                        <button onClick={() => router.push('/products')} className="text-amber-300 hover:text-amber-100 transition-colors">Products</button>
                        <button onClick={() => router.push('/about')} className="text-amber-300 hover:text-amber-100 transition-colors">About</button>
                        <button onClick={() => router.push('/contact')} className="text-amber-300 hover:text-amber-100 transition-colors">Contact</button>
                    </div>
                </div>
            )}

            <div className="relative z-10 container mx-auto px-6 py-12">
                {/* Navigation Header */}
                <div className="flex justify-between items-center mb-12">
                    {/* Mobile Menu Button */}
                    <button
                        onClick={() => setMobileMenuOpen(true)}
                        className="md:hidden text-amber-300 hover:text-amber-100 transition-colors"
                    >
                        <Menu className="w-8 h-8" />
                    </button>

                    {/* Desktop Navigation */}
                    <div className="hidden md:flex gap-6">
                        <button onClick={() => router.push('/')} className="text-amber-300 hover:text-amber-100 transition-colors">Home</button>
                        <button onClick={() => router.push('/products')} className="text-amber-300 hover:text-amber-100 transition-colors">Products</button>
                        <button onClick={() => router.push('/about')} className="text-amber-300 hover:text-amber-100 transition-colors">About</button>
                        <button onClick={() => router.push('/contact')} className="text-amber-300 hover:text-amber-100 transition-colors">Contact</button>
                    </div>

                    {/* Cart Icon */}
                    <div className="relative">
                        <button
                            className="text-amber-300 hover:text-amber-100 transition-colors relative"
                            onClick={() => router.push('/mocha/cart')}
                        >
                            <ShoppingCart className="w-8 h-8" />
                            {cartItems > 0 && (
                                <span className="absolute -top-2 -right-2 bg-amber-500 text-gray-900 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
                                    {cartItems}
                                </span>
                            )}
                        </button>
                    </div>
                </div>

                {/* Main Header */}
                <div className="text-center mb-16">
                    <div className="flex items-center justify-center mb-6">
                        <Coffee className="w-12 h-12 text-amber-400 mr-4" />
                        <h1 className="text-6xl font-bold bg-gradient-to-r from-amber-300 via-orange-400 to-amber-500 bg-clip-text text-transparent">
                            Mocha
                        </h1>
                    </div>
                    <p className="text-xl text-amber-100 max-w-2xl mx-auto leading-relaxed">
                        Discover our premium collection of coffee essentials. From artisan-roasted beans to handcrafted mugs,
                        elevate your coffee experience with Mocha.
                    </p>
                </div>

                {/* Product Cards */}
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
                    {isLoading ? (
                        // Show skeleton loaders when loading
                        Array.from({ length: 3 }).map((_, index) => (
                            <ProductSkeleton key={`skeleton-${index}`} />
                        ))
                    ) : (
                        // Show actual products when loaded
                        products.map((product) => (
                            <div key={product._id} className="group relative">
                                {/* Card */}
                                <div className="bg-gradient-to-br from-amber-50/10 to-orange-100/5 backdrop-blur-sm border border-amber-500/20 rounded-2xl p-8 hover:border-amber-400/40 transition-all duration-500 hover:shadow-2xl hover:shadow-amber-500/10 hover:-translate-y-2">
                                    {/* Badge */}
                                    <div className="absolute -top-3 left-6">
                                        <span className="bg-gradient-to-r from-amber-400 to-orange-500 text-gray-900 px-4 py-1 rounded-full text-sm font-bold shadow-lg">
                                            {product.type === 'free_coffee' ? 'Limited Time' : 'Limited Stock'}
                                        </span>
                                    </div>

                                    {/* Product Image */}
                                    <div className="text-center mb-6">
                                        <Image alt={product.name} width={300} height={300} src={`https://res.cloudinary.com/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/image/upload/${product.image}`} className="text-8xl mb-4 group-hover:scale-110 transition-transform duration-300" />
                                        <div className="flex items-center justify-center mb-2">
                                            <div className="flex text-amber-400">
                                                {[...Array(5)].map((_, i) => (
                                                    <Star key={i} className={`w-4 h-4 ${i < Math.floor(product.rating) ? 'fill-current' : ''}`} />
                                                ))}
                                            </div>
                                            <span className="ml-2 text-amber-200 text-sm">
                                                {product.rating} ({product.reviews} reviews)
                                            </span>
                                        </div>
                                    </div>

                                    {/* Product Info */}
                                    <div className="text-center mb-6">
                                        <h3 className="text-2xl font-bold text-amber-100 mb-3">
                                            {product.name}
                                        </h3>
                                        <p className="text-amber-200/80 leading-relaxed mb-4">
                                            {product.description}
                                        </p>

                                        {/* Features */}
                                        <div className="flex flex-wrap gap-2 justify-center mb-6">
                                            {product.features.map((feature: string, index: number) => (
                                                <span key={index} className="bg-amber-500/20 text-amber-200 px-3 py-1 rounded-full text-xs">
                                                    {feature}
                                                </span>
                                            ))}
                                        </div>

                                        {/* Price */}
                                        <div className="flex items-center justify-center gap-3 mb-6">
                                            <span className="text-3xl font-bold text-amber-300">
                                                ${product.price}
                                            </span>
                                            {product.originalPrice && product.originalPrice !== product.price && (
                                                <span className="text-lg text-amber-500/60 line-through">
                                                    ${product.originalPrice}
                                                </span>
                                            )}
                                        </div>
                                    </div>

                                    {/* Quantity & Add to Cart */}
                                    <div className="space-y-4">
                                        {product.type !== 'free_coffee' && (
                                            <div className="flex items-center justify-center gap-4">
                                                <button
                                                    onClick={() => updateQuantity(product._id, -1)}
                                                    className="w-10 h-10 rounded-full bg-amber-500/20 hover:bg-amber-500/30 text-amber-300 flex items-center justify-center transition-colors"
                                                    disabled={loadingStates[product._id]}
                                                >
                                                    <Minus className="w-4 h-4" />
                                                </button>
                                                <span className="text-xl font-semibold text-amber-100 min-w-[2rem] text-center">
                                                    {quantities[product._id]}
                                                </span>
                                                <button
                                                    onClick={() => updateQuantity(product._id, 1)}
                                                    className="w-10 h-10 rounded-full bg-amber-500/20 hover:bg-amber-500/30 text-amber-300 flex items-center justify-center transition-colors"
                                                    disabled={loadingStates[product._id]}
                                                >
                                                    <Plus className="w-4 h-4" />
                                                </button>
                                            </div>
                                        )}

                                        <button
                                            onClick={() => {
                                                if (product.type === 'free_coffee') {
                                                    if (!isMinting) {
                                                        mintToken();
                                                    }
                                                } else {
                                                    console.log('coming soon');
                                                }
                                            }}
                                            disabled={loadingStates[product._id] || (product.type === 'free_coffee' && isMinting)}
                                            className="w-full bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-400 hover:to-orange-500 text-gray-900 font-bold py-4 px-6 rounded-xl transition-all duration-300 hover:shadow-lg hover:shadow-amber-500/25 flex items-center justify-center gap-2 group disabled:opacity-50 disabled:cursor-not-allowed"
                                        >
                                            {loadingStates[product._id] || (product.type === 'free_coffee' && isMinting) ? (
                                                <>
                                                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900"></div>
                                                    {product.type === 'free_coffee' ? 'Minting...' : 'Adding...'}
                                                </>
                                            ) : product.type === 'free_coffee' ? (
                                                <>
                                                    <Gift className="w-5 h-5 group-hover:rotate-12 transition-transform" />
                                                    Claim Now
                                                </>
                                            ) : (
                                                <>
                                                    <ShoppingBag className="w-5 h-5 group-hover:scale-110 transition-transform" />
                                                    coming soon
                                                </>
                                            )}
                                        </button>
                                    </div>
                                </div>

                                {/* Glow Effect */}
                                <div className="absolute inset-0 bg-gradient-to-r from-amber-500/5 to-orange-500/5 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10"></div>
                            </div>
                        ))
                    )}
                </div>

                {/* Bottom CTA */}
                <div className="text-center mt-16">
                    <p className="text-amber-200/80 text-lg mb-6">
                        Experience the perfect brew with our premium collection
                    </p>
                    <div className="flex flex-wrap justify-center gap-4">
                        <div className="flex items-center gap-2 text-amber-300">
                            <Coffee className="w-5 h-5" />
                            <span>Premium Quality</span>
                        </div>
                        <div className="flex items-center gap-2 text-amber-300">
                            <Star className="w-5 h-5 fill-current" />
                            <span>5-Star Rated</span>
                        </div>
                        <div className="flex items-center gap-2 text-amber-300">
                            <Gift className="w-5 h-5" />
                            <span>Free Shipping</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}