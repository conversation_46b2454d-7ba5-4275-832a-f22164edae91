'use client'
import { useState, useEffect } from 'react';
import { Coffee, RotateCw, Star, CheckCircle, Sparkles, Crown } from 'lucide-react';
import { getUserTokens, generateQRCode as generateQRCodeApi, getUser } from '@/app/api/api';

interface User {
    coffee_redemptions: number;
    // Add other user properties as needed
}

interface Token {
    token_id: string;
    // Add other token properties as needed
}

export default function CoffeeRedemptionScreen() {
    const [remainingCoffees, setRemainingCoffees] = useState<number>(3);
    const [isFlipped, setIsFlipped] = useState<boolean>(false);
    const [tokenId, setTokenId] = useState<string>('MOCHA-TRIPLE-001');
    const [qrCodeData, setQrCodeData] = useState<string | null>(null);
    const maxCoffees = 3;

    useEffect(() => {
        const fetchUser = async () => {
            try {
                const user: User = await getUser();
                setRemainingCoffees(maxCoffees - user.coffee_redemptions);
            } catch (error) {
                console.error('Error fetching user:', error);
            }
        };

        const intervalId = setInterval(fetchUser, 1000);

        return () => clearInterval(intervalId);
    }, []);

    useEffect(() => {
        const fetchUserTokens = async () => {
            try {
                const tokens: Token[] = await getUserTokens();
                if (tokens.length > 0) {
                    setTokenId(tokens[0].token_id);
                    const qrCode = await generateQRCodeApi(tokens[0].token_id);
                    setQrCodeData(qrCode);
                }
            } catch (error) {
                console.error('Error fetching tokens:', error);
            }
        };
        fetchUserTokens();
    }, []);

    const flipCard = () => {
        if (remainingCoffees <= 0) return; // Prevent flipping when no coffees remaining
        if (!isFlipped && !qrCodeData) {
            generateQRCodeApi(tokenId);
        }
        setIsFlipped(!isFlipped);
    };

    const redeemedCount = maxCoffees - remainingCoffees;

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-900 via-amber-900 to-orange-900 relative overflow-hidden">
            {/* Background elements */}
            <div className="absolute inset-0">
                <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 via-transparent to-orange-500/10 animate-pulse"></div>
                <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=`80` height=`80` viewBox=`0 0 80 80` xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f59e0b' fill-opacity='0.05'%3E%3Ccircle cx='40' cy='40' r='3'/%3E%3Ccircle cx='20' cy='20' r='2'/%3E%3Ccircle cx='60' cy='20' r='2'/%3E%3Ccircle cx='20' cy='60' r='2'/%3E%3Ccircle cx='60' cy='60' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>
            </div>

            {/* Floating elements */}
            <div className="absolute top-20 left-10 text-6xl opacity-20 animate-bounce">☕</div>
            <div className="absolute top-40 right-20 text-4xl opacity-15 animate-pulse delay-1000">✨</div>
            <div className="absolute bottom-32 left-20 text-5xl opacity-20 animate-bounce delay-2000">☕</div>
            <div className="absolute bottom-20 right-10 text-3xl opacity-15 animate-pulse delay-500">🫘</div>
            <div className="absolute top-1/3 right-1/4 text-2xl opacity-10 animate-bounce delay-1500">⭐</div>
            <div className="absolute bottom-1/3 left-1/4 text-4xl opacity-15 animate-pulse delay-3000">💎</div>

            <div className="relative z-10 container mx-auto px-6 py-8">
                {/* Header */}
                <div className="text-center mb-12">
                    <div className="flex items-center justify-center gap-6 mb-8">
                        <div className="relative">
                            <Crown className="w-10 h-10 text-amber-400" />
                            <div className="absolute -top-2 -right-2 w-3 h-3 bg-orange-500 rounded-full flex items-center justify-center">
                                <Sparkles className="w-3 h-3 text-white" />
                            </div>
                        </div>
                        <div className="text-center">
                            <h1 className="text-2xl font-black bg-gradient-to-r from-amber-300 via-orange-400 to-amber-500 bg-clip-text text-transparent mb-2">
                                Project Mocha Coffee
                            </h1>
                        </div>
                        <div className="relative">
                            <Coffee className="w-10 h-10 text-amber-400" />
                            <div className="absolute -top-2 -right-2 w-3 h-3 bg-orange-500 rounded-full flex items-center justify-center">
                                <Star className="w-3 h-3 text-white fill-current" />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Progress visualization */}
                <div className="mb-8">
                    <div className="flex justify-center mb-4">
                        {[1, 2, 3].map((num) => (
                            <div key={num} className="flex items-center">
                                <div className="relative">
                                    <div
                                        className={`w-16 h-16 rounded-xl flex items-center justify-center border-2 transition-all duration-300 transform ${redeemedCount >= num
                                            ? 'border-amber-400 bg-gradient-to-br from-amber-500 to-orange-600 shadow-md shadow-amber-500/40 scale-105'
                                            : 'border-amber-500/40 bg-amber-50/5 backdrop-blur-sm hover:scale-100'
                                            }`}
                                    >
                                        {redeemedCount >= num ? (
                                            <CheckCircle className="w-6 h-6 text-white" />
                                        ) : (
                                            <Coffee className="w-6 h-6 text-amber-300" />
                                        )}
                                    </div>
                                    {redeemedCount >= num && (
                                        <div className="absolute -top-1 -right-1 bg-green-500 text-white text-xs px-1 py-0.5 rounded-full font-bold">
                                            ✓
                                        </div>
                                    )}
                                </div>
                                {num < 3 && (
                                    <div
                                        className={`w-16 h-2 mx-2 rounded-full transition-all duration-300 ${redeemedCount >= num
                                            ? 'bg-gradient-to-r from-amber-500 to-orange-600 shadow-md'
                                            : 'bg-amber-500/20'
                                            }`}
                                    />
                                )}
                            </div>
                        ))}
                    </div>
                    <div className="text-center">
                        <div className="inline-flex items-center gap-1 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-400/30 px-4 py-2 rounded-full">
                            <Star className="w-4 h-4 text-green-400 fill-current" />
                            <span className="text-green-300 font-medium">
                                {redeemedCount} of {maxCoffees} premium coffees claimed
                            </span>
                        </div>
                    </div>
                </div>

                {/* Enhanced Coffee Card with Redeemed Stamp */}
                <div className="max-w-2xl mx-auto">
                    <div className="group relative">
                        <div className="perspective-1000">
                            <div
                                className={`relative w-full h-[450px] preserve-3d ${remainingCoffees > 0 ? 'cursor-pointer' : ''} transition-transform duration-700 ${isFlipped ? 'rotate-y-180' : ''
                                    }`}
                                onClick={flipCard}
                            >
                                {/* Front of Card */}
                                <div className={`absolute inset-0 backface-hidden rounded-3xl shadow-2xl border-2 p-10 flex flex-col justify-between transition-all duration-500 ${remainingCoffees === 0
                                    ? 'bg-gray-800/50 border-gray-600/30 opacity-70'
                                    : 'bg-gradient-to-br from-amber-50/15 to-orange-100/10 backdrop-blur-md border-amber-400/40 hover:border-amber-300/60 hover:shadow-amber-500/20'
                                    }`}>
                                    {/* Card Header */}
                                    <div className="flex justify-between items-start">
                                        <div>
                                            <div className="flex items-center gap-3 mb-4">
                                                <Crown className="w-8 h-8 text-amber-400" />
                                                <h3 className="text-4xl font-black text-amber-100">
                                                    NIGHTS OF CODE
                                                </h3>
                                            </div>
                                            <p className="text-amber-200/90 mb-6 text-lg font-medium">
                                                ID: {tokenId}
                                            </p>
                                            <div className="flex items-center gap-3 mb-4">
                                                <div className="flex text-amber-400">
                                                    {[...Array(5)].map((_, i) => (
                                                        <Star key={i} className="w-5 h-5 fill-current" />
                                                    ))}
                                                </div>
                                                <span className="text-amber-200 font-semibold">Artisan Quality</span>
                                            </div>
                                        </div>
                                        {remainingCoffees > 0 && (
                                            <div className="bg-amber-400/20 p-3 rounded-full text-amber-300 hover:text-amber-100 hover:bg-amber-400/30 transition-all">
                                                <RotateCw className="w-7 h-7" />
                                            </div>
                                        )}
                                    </div>

                                    {/* Card Footer with Redeemed Stamp */}
                                    <div className="text-center relative">
                                        {remainingCoffees === 0 && (
                                            <div className="absolute left-10 bottom-20">
                                                <img
                                                    src="https://static.vecteezy.com/system/resources/previews/021/433/031/non_2x/redeemed-rubber-stamp-free-png.png"
                                                    alt="Redeemed"
                                                    className="w-32 h-32 opacity-90"
                                                />
                                            </div>
                                        )}
                                        {remainingCoffees > 0 ? (
                                            <p className="text-amber-200/80 text-sm mb-2">Click to reveal QR code</p>
                                        ) : (
                                            <p className="text-amber-200/80 text-sm mb-2">Redemption limit reached</p>
                                        )}
                                        <div className="text-3xl font-bold text-amber-200">
                                            {remainingCoffees} coffee{remainingCoffees !== 1 ? 's' : ''} remaining
                                        </div>
                                    </div>
                                </div>

                                {/* Back of Card - QR Code */}
                                {remainingCoffees > 0 && (
                                    <div className="absolute inset-0 backface-hidden rounded-3xl shadow-2xl border-2 p-10 bg-gradient-to-br from-amber-50/15 to-orange-100/5 backdrop-blur-md border-amber-400/40 flex flex-col items-center justify-center rotate-y-180">
                                        <div className="mb-8">
                                            <p className="text-amber-200/80">Present this QR code at any participating café</p>
                                        </div>

                                        {/* QR Code Display */}
                                        <div className="bg-white p-6 rounded-2xl shadow-lg mb-6">
                                            {qrCodeData ? (
                                                <img src={qrCodeData} alt="QR Code" className="w-100 aspect-square" />
                                            ) : (
                                                <div className="w-48 h-48 flex items-center justify-center bg-gray-100 rounded-lg">
                                                    <div className="text-gray-500 text-center">
                                                        <div className="w-8 h-8 border-2 border-gray-400 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                                                        <p className="text-sm">Generating QR...</p>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Glow Effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 to-orange-500/10 rounded-3xl blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-700 -z-10 scale-110"></div>
                    </div>
                </div>
            </div>

            <style jsx>{`
                .perspective-1000 {
                    perspective: 1000px;
                }
                .preserve-3d {
                    transform-style: preserve-3d;
                }
                .backface-hidden {
                    backface-visibility: hidden;
                }
                .rotate-y-180 {
                    transform: rotateY(180deg);
                }
                .grid-cols-25 {
                    grid-template-columns: repeat(25, minmax(0, 1fr));
                }
                .border-3 {
                    border-width: 3px;
                }
            `}</style>
        </div>
    );
}
