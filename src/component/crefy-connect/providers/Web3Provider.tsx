'use client'
import { createConfig, WagmiProvider } from 'wagmi';
import { mainnet, goerli, polygon } from 'wagmi/chains';
import { getDefaultConfig } from 'connectkit';

const config = createConfig(
    getDefaultConfig({
        appName: 'Crefy Connect',
        chains: [mainnet, goerli, polygon],
        walletConnectProjectId: '**********',
    })
);

export const Web3Provider = ({ children }: { children: React.ReactNode }) => {
    return <WagmiProvider config={config}>{children}</WagmiProvider>;
};