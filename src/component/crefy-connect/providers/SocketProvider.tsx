'use client'
import React, { createContext, useContext, useEffect } from 'react';
import { SocketService } from '../services/socket.service';

type SocketContextType = {
    socketService: SocketService | null;
    initializeSocket: (socketId: string) => void;
};

const SocketContext = createContext<SocketContextType>({
    socketService: null,
    initializeSocket: () => { },
});

export const SocketProvider = ({
    children,
    socketService
}: {
    children: React.ReactNode;
    socketService: SocketService;
}) => {

    const initializeSocket = (socketId: string) => {
        socketService.init(socketId);
    };

    useEffect(() => {
        return () => {
            socketService?.disconnect();
        };
    }, [socketService]);

    return (
        <SocketContext.Provider value={{ socketService, initializeSocket }}>
            {children}
        </SocketContext.Provider>
    );
};

export const useSocket = () => {
    const context = useContext(SocketContext);
    if (!context) {
        throw new Error('useSocket must be used within a SocketProvider');
    }
    return context;
};