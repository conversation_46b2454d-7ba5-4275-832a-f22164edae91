'use client'

import type React from "react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { Web3Provider } from "./Web3Provider"
import { SocketProvider } from "./SocketProvider"
import { AuthProvider } from "../context/AuthContext"
import { WalletProvider } from "../context/WalletContext"
import { SocketService } from "../services/socket.service"
import type { CrefyConfig } from "../core/types"
import { ConfigProvider } from "../context/ConfigContext"

// Create a client
const queryClient = new QueryClient()
const socketService = SocketService.getInstance()

export const CrefyProvider = ({
    children,
    config
}: { children: React.ReactNode, config: CrefyConfig }) => {
    // Ensure config is serializable
    const serializedConfig = {
        ...config,
        socialLogins: { ...config.socialLogins },
        appearance: config.appearance ? { ...config.appearance } : undefined
    }

    return (
        <ConfigProvider config={serializedConfig}>
            <QueryClientProvider client={queryClient}>
                <Web3Provider>
                    <SocketProvider socketService={socketService}>
                        <WalletProvider>
                            <AuthProvider socketService={socketService}>
                                {children}
                            </AuthProvider>
                        </WalletProvider>
                    </SocketProvider>
                </Web3Provider>
            </QueryClientProvider>
        </ConfigProvider>
    )
}
