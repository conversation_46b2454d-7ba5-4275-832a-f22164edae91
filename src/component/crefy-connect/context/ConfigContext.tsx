'use client'

import React, { createContext, useContext } from 'react';
import type { CrefyConfig } from '../core/types';

const ConfigContext = createContext<CrefyConfig>({
    socialLogins: {
        google: true,
        github: true,
        discord: true,
        telegram: true,
        twitter: true,
        wallet: true,
        email: true,
        phone: true,
    },
    crefyId: '',
});

export const useConfig = () => useContext(ConfigContext);

export const ConfigProvider: React.FC<{
    config?: Partial<CrefyConfig>;
    children: React.ReactNode;
}> = ({ config = {}, children }) => {
    const defaultConfig: CrefyConfig = {
        socialLogins: {
            google: true,
            github: true,
            discord: true,
            telegram: true,
            twitter: true,
            wallet: true,
            email: true,
            phone: true,
        },
        appearance: {},
        crefyId: '',
    };

    const mergedConfig: CrefyConfig = {
        ...defaultConfig,
        ...config,
        socialLogins: {
            ...defaultConfig.socialLogins,
            ...config.socialLogins,
        },
    };

    return (
        <ConfigContext.Provider value={mergedConfig}>
            {children}
        </ConfigContext.Provider>
    );
};