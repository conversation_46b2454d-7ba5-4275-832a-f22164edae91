// auth.context.tsx
'use client';

import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import type { AuthState, User, Wallet } from '../core/types';
import { AuthService } from '../services/auth.service';
import { SocketService } from '../services/socket.service';
import { useWalletContext } from './WalletContext';
import { useConfig } from './ConfigContext';

type LoginParams = {
    provider: string;
    credentials?: Record<string, string>;
    action?: 'initiate' | 'verify';
};

type AuthContextType = {
    state: AuthState;
    login: (params: LoginParams) => Promise<any>;
    logout: () => Promise<void>;
    socketService: SocketService;
    wallet: any | null;
    header: Header;
    setHeader: (header: Header) => void;
};

type Header = {
    backButton: boolean;
    title: string;
    screen?: 'wallet' | 'receive' | 'send' | 'onramp' | 'transactions' | 'assets' | 'settings';
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);
const AUTH_STORAGE_KEY = 'crefy_auth_state';
const WALLET_STORAGE_KEY = 'crefy_wallet_data';

export const AuthProvider = ({ children, socketService: injectedSocketService }: { children: React.ReactNode, socketService: SocketService }) => {
    const [state, setState] = useState<AuthState>(() => {
        if (typeof window !== 'undefined') {
            const storedAuth = localStorage.getItem(AUTH_STORAGE_KEY);
            return storedAuth
                ? JSON.parse(storedAuth)
                : {
                    user: null,
                    isAuthenticated: false,
                    isLoading: false,
                    success: null,
                    error: null,
                };
        }
        return {
            user: null,
            isAuthenticated: false,
            isLoading: false,
            success: null,
            error: null,
        };
    });

    const [header, setHeader] = useState<Header>({
        backButton: false,
        title: '',
        screen: 'wallet'
    })

    const [wallet, setWallet] = useState<any | null>(() => {
        if (typeof window !== 'undefined') {
            const storedWallet = localStorage.getItem(WALLET_STORAGE_KEY);
            return storedWallet ? JSON.parse(storedWallet) : null;
        }
        return null;
    });

    const authServiceRef = useRef<AuthService | null>(null);
    const windowCloseCheckRef = useRef<NodeJS.Timeout | null>(null);
    const walletContext = useWalletContext();
    const config = useConfig();
    const socketService = injectedSocketService || SocketService.getInstance();

    // Persist auth state to localStorage
    useEffect(() => {
        if (typeof window !== 'undefined') {
            localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(state));
        }
    }, [state]);

    // Persist wallet to localStorage
    useEffect(() => {
        if (typeof window !== 'undefined') {
            if (wallet) {
                localStorage.setItem(WALLET_STORAGE_KEY, JSON.stringify(wallet));
            } else {
                localStorage.removeItem(WALLET_STORAGE_KEY);
            }
        }
    }, [wallet]);

    useEffect(() => {
        authServiceRef.current = new AuthService();

        const handleAuthSuccess = async ({ user, wallet }: { user: User; wallet: Wallet }) => {
            console.log('Handling auth success in provider', user, wallet);
            const newState = {
                user,
                isAuthenticated: true,
                isLoading: false,
                success: null,
                error: null,
            };
            setState(newState);
            setWallet(wallet);

            // Connect wallet if available
            if (wallet) {
                try {
                    await walletContext.connectWallet(wallet.address, wallet.chainId);
                } catch (error) {
                    console.error('Failed to connect wallet:', error);
                    setState(prev => ({
                        ...prev,
                        error: error instanceof Error ? error.message : 'Wallet connection failed',
                    }));
                }
            }

            // Cleanup
            authServiceRef.current?.cleanup();
            if (windowCloseCheckRef.current) {
                clearInterval(windowCloseCheckRef.current);
                windowCloseCheckRef.current = null;
            }
        };

        const handleAuthError = (error: any) => {
            console.log('Handling auth error', error);
            setState(prev => ({
                ...prev,
                isLoading: false,
                error: error instanceof Error ? error.message : 'Authentication failed',
            }));

            authServiceRef.current?.cleanup();
            if (windowCloseCheckRef.current) {
                clearInterval(windowCloseCheckRef.current);
                windowCloseCheckRef.current = null;
            }
        };

        socketService.onAuthSuccess(handleAuthSuccess);
        socketService.onAuthError(handleAuthError);

        return () => {
            socketService.offAuthSuccess(handleAuthSuccess);
            socketService.offAuthError(handleAuthError);
            authServiceRef.current?.cleanup();
            if (windowCloseCheckRef.current) {
                clearInterval(windowCloseCheckRef.current);
            }
        };
    }, [walletContext, socketService]);

    const login = async ({ provider, credentials, action = 'verify' }: LoginParams) => {
        try {
            setState(prev => ({
                ...prev,
                isLoading: true,
                error: null
            }));

            if (!config.socialLogins[provider as keyof typeof config.socialLogins]) {
                throw new Error(`Login provider '${provider}' is not enabled`);
            }

            const result: any = await authServiceRef.current?.initiateLogin(provider, credentials, action);

            if (result && !(result instanceof Window)) {
                setState(prev => ({ ...prev, isLoading: false }));

                if (result?.isActive) {
                    const newState = {
                        user: result?.user,
                        isAuthenticated: true,
                        isLoading: false,
                        success: 'Verification email sent',
                        error: null
                    };
                    setState(newState);
                    setWallet(result?.wallet);

                    if (result?.wallet) {
                        console.log('Connecting wallet', result?.wallet);
                        try {
                            await walletContext.connectWallet(result?.wallet?.address, result?.wallet?.chainId);
                        } catch (error) {
                            console.error('Failed to connect wallet:', error);
                            setState(prev => ({
                                ...prev,
                                error: error instanceof Error ? error.message : 'Wallet connection failed',
                            }));
                        }
                    }
                }
                return result;
            }

            if (result instanceof Window) {
                setupWindowCloseCheck(result);
            }

            setState(prev => ({ ...prev, isLoading: false }));
            return result;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Login failed';
            setState(prev => ({
                ...prev,
                isLoading: false,
                error: errorMessage,
            }));
            throw error;
        }
    };

    const setupWindowCloseCheck = (popup: Window) => {
        windowCloseCheckRef.current = setInterval(() => {
            if (popup.closed) {
                setState(prev => ({
                    ...prev,
                    isLoading: false,
                    error: 'Authentication window was closed',
                }));

                if (windowCloseCheckRef.current) {
                    clearInterval(windowCloseCheckRef.current);
                    windowCloseCheckRef.current = null;
                }
            }
        }, 500);
    };

    const logout = async () => {
        try {
            setState(prev => ({ ...prev, isLoading: true }));

            // await authServiceRef.current?.logout();
            // await walletContext.disconnectWallet();

            const newState = {
                user: null,
                isAuthenticated: false,
                isLoading: false,
                success: null,
                error: null,
            };
            setState(newState);
            setWallet(null);

            localStorage.removeItem(AUTH_STORAGE_KEY);
            localStorage.removeItem(WALLET_STORAGE_KEY);
        } catch (error) {
            setState(prev => ({
                ...prev,
                isLoading: false,
                error: error instanceof Error ? error.message : 'Logout failed',
            }));
        }
    };

    return (
        <AuthContext.Provider value={{ state, login, logout, socketService, wallet, header, setHeader }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};