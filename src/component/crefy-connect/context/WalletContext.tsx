// wallet.context.tsx
'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import type { WalletState } from '../core/types';

type WalletContextType = {
    state: WalletState;
    connectWallet: (address: string, chainId: number) => Promise<void>;
    disconnectWallet: () => Promise<void>;
};

const WalletContext = createContext<WalletContextType | undefined>(undefined);
const WALLET_STATE_STORAGE_KEY = 'crefy_wallet_state';

export const WalletProvider = ({ children }: { children: React.ReactNode }) => {
    const [state, setState] = useState<WalletState>(() => {
        if (typeof window !== 'undefined') {
            const storedState = localStorage.getItem(WALLET_STATE_STORAGE_KEY);
            return storedState
                ? JSON.parse(storedState)
                : {
                    address: null,
                    chainId: null,
                    isConnected: false,
                    connector: null,
                };
        }
        return {
            address: null,
            chainId: null,
            isConnected: false,
            connector: null,
        };
    });

    useEffect(() => {
        if (typeof window !== 'undefined') {
            localStorage.setItem(WALLET_STATE_STORAGE_KEY, JSON.stringify(state));
        }
    }, [state]);

    const connectWallet = async (address: string, chainId: number) => {
        try {
            const newState = {
                address,
                chainId,
                isConnected: true,
                connector: 'crefy',
            };
            setState(newState);
        } catch (error) {
            console.error('Failed to connect wallet:', error);
            throw error;
        }
    };

    const disconnectWallet = async () => {
        try {
            const newState = {
                address: null,
                chainId: null,
                isConnected: false,
                connector: null,
            };
            setState(newState);
            console.log('disconnectWallet', newState);
            localStorage.removeItem(WALLET_STATE_STORAGE_KEY);
        } catch (error) {
            console.error('Failed to disconnect wallet:', error);
            throw error;
        }
    };

    return (
        <WalletContext.Provider value={{ state, connectWallet, disconnectWallet }}>
            {children}
        </WalletContext.Provider>
    );
};

export const useWalletContext = () => {
    const context = useContext(WalletContext);
    if (!context) {
        throw new Error('useWalletContext must be used within a WalletProvider');
    }
    return context;
};