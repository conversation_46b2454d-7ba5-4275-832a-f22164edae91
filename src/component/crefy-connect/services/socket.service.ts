import { io, Socket } from 'socket.io-client';
import { SOCKET_URL } from '../core/constants';
import type { User, Wallet } from '../core/types';

type AuthSuccessCallback = (data: { user: User; wallet: Wallet }) => void;
type AuthErrorCallback = (error: string) => void;
type ConnectCallback = () => void;
type ConnectErrorCallback = (error: Error) => void;

export class SocketService {
    private static instance: SocketService;
    private socket: Socket | null = null;
    private callbacks: {
        authSuccess: Set<AuthSuccessCallback>;
        authError: Set<AuthErrorCallback>;
        connect: Set<ConnectCallback>;
        connectError: Set<ConnectErrorCallback>;
    } = {
            authSuccess: new Set(),
            authError: new Set(),
            connect: new Set(),
            connectError: new Set(),
        };

    private constructor() { }

    public static getInstance(): SocketService {
        if (!SocketService.instance) {
            SocketService.instance = new SocketService();
        }
        return SocketService.instance;
    }

    init(socketId: string): void {
        this.cleanup(); // Clean up any existing connection

        console.log('Initializing socket with ID:', socketId);
        this.socket = io(SOCKET_URL, {
            query: { socketId },
            transports: ['websocket'],
            autoConnect: true,
            reconnectionAttempts: 3,
            reconnectionDelay: 1000,
        });

        this.setupEventListeners();
    }

    private setupEventListeners(): void {
        if (!this.socket) return;

        this.socket.on('connect', () => {
            console.log('Socket connected');
            this.callbacks.connect.forEach(callback => callback());
        });

        this.socket.on('disconnect', () => {
            console.log('Socket disconnected');
        });

        this.socket.on('connect_error', (error: Error) => {
            console.error('Socket connection error:', error);
            this.callbacks.connectError.forEach(callback => callback(error));
        });

        this.socket.on('google-auth-success', (data: { user: User; wallet: Wallet }) => {
            console.log('Authentication success:', data);
            this.callbacks.authSuccess.forEach(callback => callback(data));
        });

        this.socket.on('oauth-error', (error: string) => {
            console.error('Authentication error:', error);
            this.callbacks.authError.forEach(callback => callback(error));
        });

        // Add more event listeners as needed
        this.socket.on('github-auth-success', (data: { user: User; wallet: Wallet }) => {
            console.log('GitHub authentication success:', data);
            this.callbacks.authSuccess.forEach(callback => callback(data));
        });
    }

    // Event subscription methods
    onAuthSuccess(callback: AuthSuccessCallback): () => void {
        this.callbacks.authSuccess.add(callback);
        return () => this.callbacks.authSuccess.delete(callback);
    }

    onAuthError(callback: AuthErrorCallback): () => void {
        this.callbacks.authError.add(callback);
        return () => this.callbacks.authError.delete(callback);
    }

    onConnect(callback: ConnectCallback): () => void {
        this.callbacks.connect.add(callback);
        return () => this.callbacks.connect.delete(callback);
    }

    onConnectError(callback: ConnectErrorCallback): () => void {
        this.callbacks.connectError.add(callback);
        return () => this.callbacks.connectError.delete(callback);
    }

    // Cleanup methods
    offAuthSuccess(callback: AuthSuccessCallback): void {
        this.callbacks.authSuccess.delete(callback);
    }

    offAuthError(callback: AuthErrorCallback): void {
        this.callbacks.authError.delete(callback);
    }

    offConnect(callback: ConnectCallback): void {
        this.callbacks.connect.delete(callback);
    }

    disconnect(): void {
        if (this.socket) {
            console.log('Disconnecting socket');
            this.socket.disconnect();
            this.socket = null;
            this.clearAllCallbacks();
        }
    }

    private clearAllCallbacks(): void {
        this.callbacks.authSuccess.clear();
        this.callbacks.authError.clear();
        this.callbacks.connect.clear();
        this.callbacks.connectError.clear();
    }

    cleanup(): void {
        this.disconnect();
    }

    getSocketId(): string | null {
        return this.socket?.id || null;
    }

    isConnected(): boolean {
        return this.socket?.connected || false;
    }
}