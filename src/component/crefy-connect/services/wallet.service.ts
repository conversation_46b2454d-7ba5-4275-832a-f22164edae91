import { API_URL } from '../core/constants';
import type { Wallet } from '../core/types';

export class WalletService {
    private baseUrl: string;

    constructor() {
        this.baseUrl = `${API_URL}/wallets`;
    }

    /**
     * Creates a new wallet for a user
     * @param userId - The user's unique identifier
     * @returns Promise<Wallet> - The created wallet
     */
    async createWallet(userId: string): Promise<Wallet> {
        try {
            const response = await fetch(`${this.baseUrl}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ userId }),
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`Failed to create wallet: ${response.statusText}`);
            }

            return response.json();
        } catch (error) {
            console.error('Error creating wallet:', error);
            throw error;
        }
    }

    /**
     * Gets a wallet by user ID
     * @param userId - The user's unique identifier
     * @returns Promise<Wallet | null> - The wallet or null if not found
     */
    async getWalletByUserId(userId: string): Promise<Wallet | null> {
        try {
            const response = await fetch(`${this.baseUrl}/api/oauth/user/${userId}`, {
                credentials: 'include',
            });

            if (response.status === 404) {
                return null;
            }

            if (!response.ok) {
                throw new Error(`Failed to get wallet: ${response.statusText}`);
            }

            return response.json();
        } catch (error) {
            console.error('Error fetching wallet:', error);
            throw error;
        }
    }

    /**
     * Links a wallet address to a user
     * @param userId - The user's unique identifier
     * @param address - The wallet address to link
     * @param chainId - The chain ID where the wallet exists
     * @returns Promise<Wallet> - The updated wallet
     */
    async linkWallet(userId: string, address: string, chainId: number): Promise<Wallet> {
        try {
            const response = await fetch(`${this.baseUrl}/link`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ userId, address, chainId }),
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`Failed to link wallet: ${response.statusText}`);
            }

            return response.json();
        } catch (error) {
            console.error('Error linking wallet:', error);
            throw error;
        }
    }

    /**
     * Unlinks a wallet from a user
     * @param userId - The user's unique identifier
     * @returns Promise<void>
     */
    async unlinkWallet(userId: string): Promise<void> {
        try {
            const response = await fetch(`${this.baseUrl}/unlink`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ userId }),
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`Failed to unlink wallet: ${response.statusText}`);
            }
        } catch (error) {
            console.error('Error unlinking wallet:', error);
            throw error;
        }
    }

    /**
     * Signs a message with the user's wallet
     * @param userId - The user's unique identifier
     * @param message - The message to sign
     * @returns Promise<string> - The signature
     */
    async signMessage(userId: string, message: string): Promise<string> {
        try {
            const response = await fetch(`${this.baseUrl}/sign`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ userId, message }),
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`Failed to sign message: ${response.statusText}`);
            }

            const { signature } = await response.json();
            return signature;
        } catch (error) {
            console.error('Error signing message:', error);
            throw error;
        }
    }

    /**
     * Verifies a signed message
     * @param userId - The user's unique identifier
     * @param message - The original message
     * @param signature - The signature to verify
     * @returns Promise<boolean> - Whether the signature is valid
     */
    async verifySignature(
        userId: string,
        message: string,
        signature: string
    ): Promise<boolean> {
        try {
            const response = await fetch(`${this.baseUrl}/verify`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ userId, message, signature }),
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`Failed to verify signature: ${response.statusText}`);
            }

            const { isValid } = await response.json();
            return isValid;
        } catch (error) {
            console.error('Error verifying signature:', error);
            throw error;
        }
    }

    /**
     * Gets all transactions for a wallet
     * @param userId - The user's unique identifier
     * @param chainId - The chain ID to filter by (optional)
     * @returns Promise<any[]> - Array of transactions
     */
    async getTransactions(userId: string, chainId?: number): Promise<any[]> {
        try {
            const url = chainId
                ? `${this.baseUrl}/transactions?userId=${userId}&chainId=${chainId}`
                : `${this.baseUrl}/transactions?userId=${userId}`;

            const response = await fetch(url, {
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`Failed to get transactions: ${response.statusText}`);
            }

            return response.json();
        } catch (error) {
            console.error('Error fetching transactions:', error);
            throw error;
        }
    }

    /**
     * Sends a transaction
     * @param userId - The user's unique identifier
     * @param transaction - The transaction data
     * @returns Promise<string> - The transaction hash
     */
    async sendTransaction(userId: string, transaction: any): Promise<string> {
        try {
            const response = await fetch(`${this.baseUrl}/send`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ userId, ...transaction }),
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error(`Failed to send transaction: ${response.statusText}`);
            }

            const { txHash } = await response.json();
            return txHash;
        } catch (error) {
            console.error('Error sending transaction:', error);
            throw error;
        }
    }
}