import { API_URL } from '../core/constants';
import { SocketService } from './socket.service';
// import { useConfig } from '../context/ConfigContext';
// import type { CrefyConfig } from '../core/types';

export class AuthService {
    private socketService: SocketService;
    private oauthWindow: Window | null = null;
    private oauthWindowCheckInterval: NodeJS.Timeout | null = null;
    private socketId: string | null = null;
    // private config: CrefyConfig;

    constructor() {
        this.socketService = SocketService.getInstance();
        // this.config = useConfig();
    }

    async initiateLogin(
        provider: string,
        credentials?: Record<string, string>,
        action: 'initiate' | 'verify' = 'initiate'
    ): Promise<Window | void> {
        this.cleanup();
        this.socketId = `socket_${Date.now()}`;

        // Handle email/phone OTP flow
        if (provider === 'email' || provider === 'phone') {
            const endpoint = action === 'initiate'
                ? `${API_URL}/${provider}/initiate`
                : `${API_URL}/${provider}/verify`;

            console.log('initiating login for email or phone', endpoint);
            try {
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        ...credentials,
                        socketId: this.socketId,
                        crefyId: 'ak_c6546ef02bfb202e25eaac057e079ae3'
                    }),
                });

                console.log('response', response);

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || 'Failed to process OTP request');
                }

                const data = await response.json();
                console.log('data', data);
                return data;
            } catch (error) {
                console.error('OTP request failed:', error);
                throw error;
            }
        }

        // Handle OAuth providers (original flow)
        return new Promise((resolve, reject) => {
            const connectionTimeout = setTimeout(() => {
                reject(new Error('Connection timeout'));
                this.cleanup();
            }, 10000);

            this.socketService.init(this.socketId || '');
            this.socketService.onConnect(() => {
                clearTimeout(connectionTimeout);

                const width = 500;
                const height = 600;
                const left = window.screenX + (window.outerWidth - width) / 2;
                const top = window.screenY + (window.outerHeight - height) / 2;

                const url = new URL(`${API_URL}/oauth/${provider}`);
                url.searchParams.append('socketId', this.socketId!);

                if (credentials) {
                    for (const [key, value] of Object.entries(credentials)) {
                        url.searchParams.append(key, value);
                    }
                }

                this.oauthWindow = window.open(
                    url.toString(),
                    'oauth-window',
                    `width=${width},height=${height},left=${left},top=${top}`
                );

                if (!this.oauthWindow) {
                    reject(new Error('Popup window blocked by browser'));
                    return;
                }

                this.oauthWindowCheckInterval = setInterval(() => {
                    if (this.oauthWindow?.closed) {
                        reject(new Error('Authentication window closed by user'));
                        this.cleanup();
                    }
                }, 500);

                resolve(this.oauthWindow);
            });

            this.socketService.onConnectError((error: any) => {
                clearTimeout(connectionTimeout);
                reject(error);
                this.cleanup();
            });
        });
    }

    async resendOtp(provider: 'email' | 'phone', identifier: string): Promise<void> {
        try {
            const response = await fetch(`${API_URL}/oauth/${provider}/resend`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    [provider === 'email' ? 'email' : 'phoneNumber']: identifier,
                    socketId: this.socketId
                }),
                credentials: 'include'
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to resend OTP');
            }
        } catch (error) {
            console.error('Resend OTP failed:', error);
            throw error;
        }
    }

    async logout(): Promise<void> {
        try {
            await fetch(`${API_URL}/oauth/logout`, {
                method: 'POST',
                credentials: 'include',
            });
        } finally {
            this.cleanup();
        }
    }

    cleanup() {
        if (this.oauthWindowCheckInterval) {
            clearInterval(this.oauthWindowCheckInterval);
            this.oauthWindowCheckInterval = null;
        }
        if (this.oauthWindow) {
            this.oauthWindow.close();
            this.oauthWindow = null;
        }
        if (this.socketId) {
            this.socketService.disconnect();
            this.socketId = null;
        }
    }
}
