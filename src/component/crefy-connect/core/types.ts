export type User = {
    id: string;
    email?: string;
    name?: string;
    avatar?: string;
};

export type Wallet = {
    address: string;
    chainId: number;
    userId: string;
    createdAt: Date;
};


export type AuthState = {
    user: User | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    success: string | null;
    error: string | null;
};

export type WalletState = {
    address: string | null;
    chainId: number | null;
    isConnected: boolean;
    connector: string | null;
};

export type SocialLoginConfig = {
    google?: boolean;
    github?: boolean;
    discord?: boolean;
    telegram?: boolean;
    twitter?: boolean;
    wallet?: boolean;
    email?: boolean;
    phone?: boolean;
};

export type CrefyConfig = {
    socialLogins: SocialLoginConfig;
    crefyId: string;
    appearance?: {
        primaryColor?: string;
        secondaryColor?: string;
    };
};

export type SocialAuthProvider = 'google' | 'facebook' | 'twitter'; // Add more as needed