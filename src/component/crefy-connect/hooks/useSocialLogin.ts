import { useAuth } from '../context/AuthContext';

export const useSocialLogin = () => {
    const { state: authState, login, logout, wallet } = useAuth();
    const initiateLogin = (provider: string) => {
        login({ provider: provider as any });
    };

    return {
        user: authState.user,
        initiateLogin,
        isLoading: authState.isLoading,
        error: authState.error,
        wallet: wallet,
        logout,
    };
};