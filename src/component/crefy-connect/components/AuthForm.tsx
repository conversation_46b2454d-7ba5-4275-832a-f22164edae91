
// components/auth/AuthForm.tsx
import React from 'react';
import { EmailPhoneForm } from './auth/EmailPhoneForm';
import { SocialLoginButtons } from './auth/SocialLoginButtons';
import { WalletConnectButton } from './auth/WalletConnectButton';
import { AuthFooter } from './AuthFooter';
import { ErrorDisplay } from './auth/ErrorDisplay';
import type { User, Wallet } from '../core/types';
import { useConfig } from '../context/ConfigContext';
import { WalletInterface } from './wallet/WalletInterface';

interface AuthFormProps {
    user?: User;
    wallet?: Wallet;
    email: string;
    setEmail: (email: string) => void;
    phoneNumber: string;
    setPhoneNumber: (phone: string) => void;
    usePhone: boolean;
    setUsePhone: (usePhone: boolean) => void;
    handleEmailSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
    handlePhoneSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
    login: ({ provider }: { provider: string }) => Promise<void>;
    isLoading: boolean;
    handleClose: () => void;
    error?: string;
}

export const AuthForm: React.FC<AuthFormProps> = ({
    user,
    email,
    setEmail,
    phoneNumber,
    setPhoneNumber,
    usePhone,
    setUsePhone,
    handleEmailSubmit,
    handlePhoneSubmit,
    login,
    isLoading,
    error,
}) => {
    const config = useConfig();
    // Calculate which sections to show
    const showEmailPhone = config.socialLogins.email || config.socialLogins.phone;
    const hasSocialLogins = config.socialLogins.google || config.socialLogins.github ||
        config.socialLogins.discord || config.socialLogins.telegram ||
        config.socialLogins.twitter;
    const showSocialDivider = showEmailPhone && hasSocialLogins;
    const showWalletDivider = (showEmailPhone || hasSocialLogins) && config.socialLogins.wallet;

    if (user) {
        return <WalletInterface />;
    }

    return (
        <>
            <EmailPhoneForm
                showEmailPhone={showEmailPhone || false}
                usePhone={usePhone}
                setUsePhone={setUsePhone}
                email={email}
                setEmail={setEmail}
                phoneNumber={phoneNumber}
                setPhoneNumber={setPhoneNumber}
                handleEmailSubmit={handleEmailSubmit}
                handlePhoneSubmit={handlePhoneSubmit}
                isLoading={isLoading}
                showSocialDivider={showSocialDivider || false}
            />

            <SocialLoginButtons
                hasSocialLogins={hasSocialLogins || false}
                login={login}
                isLoading={isLoading}
            />

            <WalletConnectButton
                showWallet={config.socialLogins.wallet || false}
                showWalletDivider={showWalletDivider || false}
                // login={login}
                isLoading={isLoading}
            />

            <AuthFooter />

            <ErrorDisplay error={error} />
        </>
    );
};