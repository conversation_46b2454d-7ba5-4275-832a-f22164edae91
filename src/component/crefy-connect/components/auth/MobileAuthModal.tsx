
// components/auth/MobileAuthModal.tsx
import React from 'react';
import { LoadingState } from './LoadingState';
import { AuthForm } from '../AuthForm';

type Header = {
    backButton: boolean;
    title: string;
}

interface MobileAuthModalProps {
    header: Header;
    isVisible: boolean;
    isLoading: boolean;
    handleClose: () => void;
    authFormProps: any;
    isAuthenticated: boolean;
}

export const MobileAuthModal: React.FC<MobileAuthModalProps> = ({
    header,
    isVisible,
    isLoading,
    handleClose,
    authFormProps,
    isAuthenticated,
}) => {
    return (
        <div className={`
      sm:hidden
      fixed inset-x-0 bottom-0 bg-white
      rounded-t-3xl max-h-[90vh] overflow-hidden
      transition-transform duration-300 ease-out
      ${isVisible ? 'translate-y-0' : 'translate-y-full'}
    `}>
            {/* Mobile Handle Bar */}
            <div className="flex justify-center pt-3 pb-2">
                <div className="w-10 h-1 bg-gray-300 rounded-full"></div>
            </div>

            {/* Mobile Content */}
            <div className="overflow-y-auto max-h-[calc(90vh-40px)]">
                <div className="p-4">
                    {/* Mobile Header */}
                    <div className="border-b border-gray-100 pb-3 mb-4">
                        <button
                            onClick={handleClose}
                            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-2xl font-light w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
                            aria-label="Close"
                        >
                            ×
                        </button>
                        <div className="text-center">
                            <p className="text-sm text-gray-600">
                                {isAuthenticated ? header.title : 'Log in or sign up to continue'}
                            </p>
                        </div>

                    </div>

                    {/* Mobile Form Content */}
                    {isLoading ? (
                        <LoadingState />
                    ) : (
                        <AuthForm {...authFormProps} />
                    )}
                </div>
            </div>
        </div>
    );
};
