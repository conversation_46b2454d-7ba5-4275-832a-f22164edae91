import React, { useRef, useState } from 'react';

interface OTPFormProps {
    email: string;
    onSubmit: (otp: string) => Promise<void>;
    onBack: () => void;
    isLoading: boolean;
    usePhone: boolean;
    error?: string;
}

export const OTPForm: React.FC<OTPFormProps> = ({
    email,
    onSubmit,
    onBack,
    isLoading,
    usePhone,
    error
}) => {
    const [otp, setOtp] = useState<string[]>(['', '', '', '', '', '']);
    const [resendLoading, setResendLoading] = useState(false);
    const [resendSuccess, setResendSuccess] = useState(false);
    const inputRefs = useRef<Array<HTMLInputElement | null>>([]);

    const handleChange = (index: number, value: string) => {
        if (value && !/^\d+$/.test(value)) return;

        const newOtp = [...otp];
        newOtp[index] = value;
        setOtp(newOtp);

        if (value && index < 5 && inputRefs.current[index + 1]) {
            inputRefs.current[index + 1]?.focus();
        }
    };

    const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Backspace' && !otp[index] && index > 0 && inputRefs.current[index - 1]) {
            inputRefs.current[index - 1]?.focus();
        }
    };

    const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
        e.preventDefault();
        const pasteData = e.clipboardData.getData('text/plain').slice(0, 6);
        if (/^\d+$/.test(pasteData)) {
            const newOtp = [...otp];
            pasteData.split('').forEach((char, i) => {
                if (i < 6) newOtp[i] = char;
            });
            setOtp(newOtp);
        }
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        const otpCode = otp.join('');
        if (otpCode.length === 6) {
            await onSubmit(otpCode);
        }
    };

    const handleResend = async () => {
        setResendLoading(true);
        setResendSuccess(false);
        try {
            // You'll need to implement this function in your auth context
            // await resendOtp({ email, phoneNumber: usePhone ? email : undefined });
            setResendSuccess(true);
            setTimeout(() => setResendSuccess(false), 3000);
        } catch (err) {
            console.error('Failed to resend OTP:', err);
        } finally {
            setResendLoading(false);
        }
    };

    return (
        <div className="bg-white rounded-2xl shadow-xl w-full max-w-md overflow-hidden">
            <div className="p-6">
                <div className="flex items-center mb-6">
                    <button
                        onClick={onBack}
                        className="text-gray-500 hover:text-gray-700 mr-2"
                        disabled={isLoading}
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                        </svg>
                    </button>
                    <h2 className="text-xl font-bold text-gray-800">Verify {usePhone ? 'Phone' : 'Email'}</h2>
                </div>

                <div className="mb-4">
                    <p className="text-gray-600 mb-1">{usePhone ? 'Phone' : 'Email'}</p>
                    <p className="font-medium">{email}</p>
                </div>

                <form onSubmit={handleSubmit}>
                    <div className="mb-6">
                        <label htmlFor="otp" className="block text-gray-700 text-sm font-medium mb-3">
                            Enter 6-digit verification code
                        </label>
                        <div className="flex justify-between space-x-2">
                            {otp.map((digit, index) => (
                                <input
                                    key={index}
                                    ref={(el) => {
                                        if (el) {
                                            inputRefs.current[index] = el;
                                        }
                                    }}
                                    type="text"
                                    maxLength={1}
                                    value={digit}
                                    onChange={(e) => handleChange(index, e.target.value)}
                                    onKeyDown={(e) => handleKeyDown(index, e)}
                                    onPaste={handlePaste}
                                    className="w-12 h-12 text-center text-xl border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    disabled={isLoading}
                                    pattern="\d*"
                                    inputMode="numeric"
                                    autoFocus={index === 0}
                                />
                            ))}
                        </div>
                        {error && (
                            <p className="mt-2 text-sm text-red-600">{error}</p>
                        )}
                        <p className="mt-3 text-xs text-gray-500">
                            We sent a code to your {usePhone ? 'phone' : 'email'}
                        </p>
                    </div>

                    <button
                        type="submit"
                        disabled={isLoading || otp.join('').length !== 6}
                        className="w-full py-3.5 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-semibold mb-3 disabled:opacity-50 disabled:cursor-not-allowed hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-[1.02] active:scale-[0.98] shadow-lg"
                    >
                        {isLoading ? 'Verifying...' : 'Verify'}
                    </button>
                </form>

                <div className="text-center">
                    <button
                        type="button"
                        className="text-blue-600 text-sm hover:underline font-medium"
                        onClick={handleResend}
                        disabled={resendLoading || isLoading}
                    >
                        {resendLoading ? 'Sending...' : 'Resend code'}
                    </button>
                    {resendSuccess && (
                        <p className="text-green-600 text-sm mt-1">Code resent successfully!</p>
                    )}
                </div>
            </div>
        </div>
    );
};