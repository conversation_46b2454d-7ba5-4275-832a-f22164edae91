
// components/auth/DesktopAuthModal.tsx
import React from 'react';
import { LoadingState } from './LoadingState';
import { AuthForm } from '../AuthForm';
import { ArrowLeft } from 'lucide-react';

type Header = {
    backButton: boolean;
    title: string;
}

interface DesktopAuthModalProps {
    header: Header;
    isLoading: boolean;
    handleClose: () => void;
    authFormProps: any;
    isAuthenticated: boolean;
}

export const DesktopAuthModal: React.FC<DesktopAuthModalProps> = ({
    header,
    isLoading,
    handleClose,
    authFormProps,
    isAuthenticated,
}) => {
    return (
        <div className="hidden sm:block bg-white rounded-2xl w-full max-w-md shadow-2xl max-h-[95vh] overflow-hidden">
            {/* Desktop Header */}
            <div className="border-b border-gray-100 p-6 pb-4 relative">
                {header.backButton && (
                    <button
                        onClick={handleClose}
                        className="absolute top-4 left-4 text-gray-400 hover:text-gray-600 text-2xl font-light w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
                        aria-label="Back"
                    >
                        <ArrowLeft size={20} className="text-gray-500" />
                    </button>
                )}
                <button
                    onClick={handleClose}
                    className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-2xl font-light w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
                    aria-label="Close"
                >
                    ×
                </button>
                <div className="text-center">
                    <p className="text-sm text-gray-600">
                        {isAuthenticated ? header.title : 'Log in or sign up to continue'}
                    </p>
                </div>
            </div>

            {/* Desktop Content */}
            <div className="overflow-y-auto max-h-[calc(95vh-80px)]">
                <div className="p-6">
                    {isLoading ? (
                        <LoadingState />
                    ) : (
                        <AuthForm {...authFormProps} />
                    )}
                </div>
            </div>
        </div>
    );
};