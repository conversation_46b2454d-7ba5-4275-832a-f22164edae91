// components/auth/WalletConnectButton.tsx
import React from 'react';

interface WalletConnectButtonProps {
    showWallet: boolean;
    showWalletDivider: boolean;
    // initiateLogin: (provider: string) => void;
    isLoading: boolean;
}

export const WalletConnectButton: React.FC<WalletConnectButtonProps> = ({
    showWallet,
    showWalletDivider,
    // initiateLogin,
    isLoading,
}) => {
    if (!showWallet) return null;

    return (
        <>
            {showWalletDivider && (
                <div className="flex items-center mb-5">
                    <div className="flex-1 border-t border-gray-200"></div>
                    <span className="px-3 text-gray-400 text-sm font-medium">OR</span>
                    <div className="flex-1 border-t border-gray-200"></div>
                </div>
            )}

            <button
                // onClick={() => initiateLogin('wallet')}
                disabled={isLoading}
                className="w-full flex items-center justify-center gap-3 py-3.5 border border-gray-200 rounded-xl hover:bg-gray-50 transition-all font-medium text-gray-700 disabled:opacity-50 hover:border-gray-300 hover:shadow-sm mb-5"
            >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <span>Connect Wallet</span>
            </button>
        </>
    );
};