// components/auth/SocialLoginButtons.tsx
import React from 'react';
import { useConfig } from '../../context/ConfigContext';
import { GoogleButton } from './social/GoogleButton';
import { SocialButtonGrid } from './social/SocialButtonGrid';

interface SocialLoginButtonsProps {
    hasSocialLogins: boolean;
    login: ({ provider }: { provider: string }) => Promise<void>;
    isLoading: boolean;
}

export const SocialLoginButtons: React.FC<SocialLoginButtonsProps> = ({
    hasSocialLogins,
    login,
    isLoading,
}) => {
    const config = useConfig();
    if (!hasSocialLogins) return null;

    return (
        <div className="space-y-3 mb-5">
            <GoogleButton
                show={config.socialLogins.google || false}
                onClick={() => login({ provider: 'google' })}
                disabled={isLoading}
            />

            <SocialButtonGrid
                login={login}
                isLoading={isLoading}
            />
        </div>
    );
};