// components/auth/EmailPhoneForm.tsx
import React from 'react';
import { useConfig } from '../../context/ConfigContext';

interface EmailPhoneFormProps {
    showEmailPhone: boolean;
    usePhone: boolean;
    setUsePhone: (usePhone: boolean) => void;
    email: string;
    setEmail: (email: string) => void;
    phoneNumber: string;
    setPhoneNumber: (phone: string) => void;
    handleEmailSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
    handlePhoneSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
    isLoading: boolean;
    showSocialDivider: boolean;
}
export const EmailPhoneForm: React.FC<EmailPhoneFormProps> = ({
    showEmailPhone,
    usePhone,
    setUsePhone,
    email,
    setEmail,
    phoneNumber,
    setPhoneNumber,
    handleEmailSubmit,
    handlePhoneSubmit,
    isLoading,
    showSocialDivider,
}) => {
    const config = useConfig();
    if (!showEmailPhone) return null;

    return (
        <>
            <form onSubmit={usePhone ? handlePhoneSubmit : handleEmailSubmit} className="mb-5">
                <div className="mb-4">
                    <input
                        type={usePhone ? "tel" : "email"}
                        placeholder={usePhone ? "Enter your phone number" : "Enter your email"}
                        value={usePhone ? phoneNumber : email}
                        onChange={(e) => usePhone ? setPhoneNumber(e.target.value) : setEmail(e.target.value)}
                        className="w-full px-4 py-3.5 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800 text-base transition-all"
                        disabled={isLoading}
                        required
                    />
                </div>

                <button
                    type="submit"
                    disabled={isLoading || (usePhone ? !phoneNumber.trim() : !email.trim())}
                    className="w-full py-3.5 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-semibold mb-3 disabled:opacity-50 disabled:cursor-not-allowed hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-[1.02] active:scale-[0.98] shadow-lg"
                >
                    Continue
                </button>

                {config.socialLogins.email && config.socialLogins.phone && (
                    <div className="text-center mb-4">
                        <button
                            type="button"
                            onClick={() => {
                                setUsePhone(!usePhone);
                                setEmail('');
                                setPhoneNumber('');
                            }}
                            className="text-blue-600 text-sm hover:underline font-medium"
                        >
                            {usePhone ? 'Use email instead' : 'Use phone instead'}
                        </button>
                    </div>
                )}
            </form>

            {showSocialDivider && (
                <div className="flex items-center mb-5">
                    <div className="flex-1 border-t border-gray-200"></div>
                    <span className="px-3 text-gray-400 text-sm font-medium">OR</span>
                    <div className="flex-1 border-t border-gray-200"></div>
                </div>
            )}
        </>
    );
};