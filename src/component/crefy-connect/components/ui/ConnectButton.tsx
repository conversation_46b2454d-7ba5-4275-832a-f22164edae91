'use client';

import React from 'react';
import { useAuth } from '../../hooks/useAuth';
import { AuthModal } from '../AuthModal';

export const ConnectButton = ({
    buttonText = 'Connect Wallet',
    className = ''
}) => {
    const { state: authState, wallet, } = useAuth();
    const [showModal, setShowModal] = React.useState(false);

    const baseButtonStyles = `
        relative px-6 py-3 font-semibold text-sm rounded-xl
        transition-all duration-200 ease-in-out
        focus:outline-none focus:ring-2 focus:ring-offset-2
        disabled:opacity-50 disabled:cursor-not-allowed
        shadow-lg hover:shadow-xl
        active:scale-95
    `;

    const connectedStyles = `
        bg-gradient-to-r from-emerald-500 to-teal-600 
        hover:from-emerald-600 hover:to-teal-700
        text-white border border-emerald-400
        focus:ring-emerald-500
    `;

    const disconnectedStyles = `
        bg-gradient-to-r from-blue-600 to-purple-600 
        hover:from-blue-700 hover:to-purple-700
        text-white border border-blue-400
        focus:ring-blue-500
    `;

    const loadingStyles = `
        bg-gradient-to-r from-gray-400 to-gray-500
        text-white cursor-wait
    `;

    const getButtonStyles = () => {
        if (authState.isLoading) return `${baseButtonStyles} ${loadingStyles}`;
        if (authState.isAuthenticated) return `${baseButtonStyles} ${connectedStyles}`;
        return `${baseButtonStyles} ${disconnectedStyles}`;
    };

    return (
        <div>
            {authState.isAuthenticated ? (
                <button
                    onClick={() => setShowModal(true)}
                    className={`${getButtonStyles()} ${className}`}
                >
                    <span className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-300 rounded-full animate-pulse"></div>
                        {wallet ? `${wallet.address.substring(0, 6)}...${wallet.address.substring(wallet.address.length - 4)}` : ''}
                    </span>
                </button>
            ) : (
                <button
                    onClick={() => setShowModal(true)}
                    className={`${getButtonStyles()} ${className}`}
                    disabled={authState.isLoading}
                >
                    {authState.isLoading ? (
                        <span className="flex items-center gap-2">
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            Connecting...
                        </span>
                    ) : (
                        buttonText
                    )}
                </button>
            )}

            {showModal && (
                <AuthModal onClose={() => setShowModal(false)} />
            )}

        </div>
    );
};