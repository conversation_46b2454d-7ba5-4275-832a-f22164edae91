import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useConfig } from '../context/ConfigContext';
import { MobileAuthModal } from './auth/MobileAuthModal';
import { DesktopAuthModal } from './auth/DesktopAuthModal';
import { OTPForm } from './auth/OTPForm';

export const AuthModal = ({ onClose }: { onClose: () => void }) => {
    const config = useConfig();
    const { login, state: { isLoading, error, user, isAuthenticated }, header } = useAuth();
    const [step, setStep] = useState<'email' | 'otp'>('email');
    const [email, setEmail] = useState('');
    const [usePhone, setUsePhone] = useState(false);
    const [phoneNumber, setPhoneNumber] = useState('');
    const [isVisible, setIsVisible] = useState(false);
    const [otpSent, setOtpSent] = useState(false);

    useEffect(() => {
        const timer = setTimeout(() => setIsVisible(true), 10);
        return () => clearTimeout(timer);
    }, []);

    const handleClose = () => {
        setIsVisible(false);
        setTimeout(onClose, 300);
    };

    const handleEmailSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        if (email.trim()) {
            try {
                const response = await login({
                    provider: 'email',
                    credentials: { email },
                    action: 'initiate'
                });

                if (response?.success && !response?.isActive) {
                    setOtpSent(true);
                    setStep('otp');
                } else {
                    setStep('email');
                }
            } catch (err) {
                console.error('Failed to send OTP:', err);
            }
        }
    };

    const handlePhoneSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        if (phoneNumber.trim()) {
            try {
                await login({
                    provider: 'phone',
                    credentials: { phoneNumber },
                    action: 'initiate'
                });
                setOtpSent(true);
                setStep('otp');
            } catch (err) {
                console.error('Failed to send OTP:', err);
            }
        }
    };

    const handleOtpSubmit = async (otp: string) => {
        try {
            const response = await login({
                provider: usePhone ? 'phone' : 'email',
                credentials: {
                    'token': otp,
                    'address': usePhone ? phoneNumber : email
                },
                action: 'verify',
            });

            console.log('response', response);

            if (response?.isActive) {
                setStep('email');
                setOtpSent(false);
            } else {
                setStep('otp');
            }
        } catch (err) {
            console.error('Verification failed:', err);
            throw err; // Let OTPForm handle the error
        }
    };

    const authFormProps = {
        user,
        email,
        setEmail,
        phoneNumber,
        setPhoneNumber,
        usePhone,
        setUsePhone,
        handleEmailSubmit,
        handlePhoneSubmit,
        isLoading,
        config,
        error,
        step,
        setStep,
        login,
        otpSent
    };

    return (
        <div className="fixed inset-0 z-50">
            {/* Backdrop */}
            <div
                className={`absolute inset-0 bg-gray transition-opacity duration-300 ${isVisible ? 'bg-opacity-[0.15]' : 'bg-opacity-0'
                    } backdrop-blur-sm`}
                onClick={handleClose}
            />

            {/* Modal Content */}
            <div className={`
                fixed inset-0 flex items-center justify-center p-4
                transition-all duration-300 ease-out
                ${isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}
                sm:relative sm:opacity-100 sm:scale-100
            `}>
                {step === 'otp' ? (
                    <OTPForm
                        email={usePhone ? phoneNumber : email}
                        onSubmit={handleOtpSubmit}
                        onBack={() => setStep('email')}
                        isLoading={isLoading}
                        usePhone={usePhone}
                        error={error || undefined}
                    />
                ) : (
                    <>
                        <MobileAuthModal
                            header={header}
                            isVisible={isVisible}
                            isLoading={isLoading}
                            handleClose={handleClose}
                            authFormProps={authFormProps}
                            isAuthenticated={isAuthenticated}
                        />

                        <DesktopAuthModal
                            header={header}
                            isLoading={isLoading}
                            handleClose={handleClose}
                            authFormProps={authFormProps}
                            isAuthenticated={isAuthenticated}
                        />
                    </>
                )}
            </div>
        </div>
    );
};