import React, { useState } from 'react';
import { Send, Download, Plus, ArrowRight, List, Eye, Settings, LogOut, Phone } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { useWalletContext } from '../../context/WalletContext';
import { QRReceiveModal } from './QRReceiveModal';

interface WalletInterfaceProps {
    onClose?: () => void;
}

export const WalletInterface: React.FC<WalletInterfaceProps> = () => {
    const { logout, setHeader } = useAuth();
    const { disconnectWallet } = useWalletContext()
    const [address] = useState("0x1E86...4828");
    const [balance] = useState("0.0452423");
    const [network] = useState("Sepolia");
    const [sendExpanded, setSendExpanded] = useState(false);
    const [currentStep, setCurrentStep] = useState<'wallet' | 'receive' | 'onramp'>('wallet');
    const [showReceiveModal, setShowReceiveModal] = useState(false);

    const menuItems = [
        { icon: Send, label: "Send", action: () => setSendExpanded(!sendExpanded), isExpandable: true },
        { icon: Download, label: "Receive", action: () => { setCurrentStep('receive'); setShowReceiveModal(true); setHeader({ backButton: true, title: 'Receive Sepolia' }) }, isExpandable: false },
        { icon: Plus, label: "Onramp", action: () => setCurrentStep('onramp'), isExpandable: false }
    ];

    const sendOptions = [
        { icon: Send, label: "Send Money", action: () => console.log('Send Money clicked') },
        { icon: Phone, label: "Till Number", action: () => console.log('Till Number clicked') },
    ];

    const navigationItems = [
        { icon: ArrowRight, label: network, sublabel: `${balance} ETH`, hasArrow: true },
        { icon: List, label: "Transactions", hasArrow: true },
        { icon: Eye, label: "View Assets", hasArrow: true },
        { icon: Settings, label: "Manage Wallet", hasArrow: true }
    ];


    return (
        <>
            {currentStep === 'wallet' && (
                <div className="w-full">
                    {/* Header */}
                    <div className="flex items-center justify-between p-4">
                        <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center relative">
                                <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                                    <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                                </div>
                                {/* Connection indicator */}
                                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                                    <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                                </div>
                            </div>
                            <div>
                                <h2 className="text-gray-900 text-sm font-medium">{address}</h2>
                                <p className="text-gray-500 text-xs">Google</p>
                            </div>
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="p-4">
                        <div className="flex space-x-2">
                            {menuItems.map((item, index) => (
                                <button
                                    key={index}
                                    onClick={item.action}
                                    className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 hover:bg-gray-100 rounded-lg transition-colors border border-gray-300 ${item.isExpandable && sendExpanded ? 'bg-blue-50 border-blue-200' : ''
                                        }`}
                                >
                                    <item.icon size={16} className="text-gray-600" />
                                    <span className="text-sm font-medium text-gray-700">{item.label}</span>
                                    {item.isExpandable && (
                                        <ArrowRight
                                            size={12}
                                            className={`text-gray-400 transition-transform duration-200 ${sendExpanded ? 'rotate-90' : ''
                                                }`}
                                        />
                                    )}
                                </button>
                            ))}
                        </div>

                        {/* Expanded Send Options */}
                        <div className={`overflow-hidden transition-all duration-300 ease-in-out ${sendExpanded ? 'max-h-48 opacity-100 mt-3' : 'max-h-0 opacity-0'
                            }`}>
                            <div className="bg-gray-50 rounded-lg p-2 border border-gray-200">
                                <div className="text-xs text-gray-500 mb-2 px-2 font-medium">Send Options</div>
                                <div className="space-y-1">
                                    {sendOptions.map((option, index) => (
                                        <button
                                            key={index}
                                            onClick={option.action}
                                            className="w-full flex items-center space-x-3 px-3 py-2 hover:bg-white hover:shadow-sm rounded-md transition-all duration-150 text-left"
                                        >
                                            <option.icon size={16} className="text-gray-600" />
                                            <span className="text-sm text-gray-700">{option.label}</span>
                                        </button>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Navigation Menu */}
                    <div className="py-2">
                        {navigationItems.map((item, index) => (
                            <button
                                key={index}
                                className="w-full flex items-center justify-between px-4 py-3 hover:bg-gray-50 transition-colors group"
                                onClick={() => console.log(`${item.label} clicked`)}
                            >
                                <div className="flex items-center space-x-3">
                                    {item.label === network ? (
                                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                    ) : (
                                        <item.icon size={18} className="text-gray-500" />
                                    )}
                                    <div className="text-left">
                                        <div className="text-sm font-medium text-gray-900">{item.label}</div>
                                        {item.sublabel && (
                                            <div className="text-xs text-gray-500">{item.sublabel}</div>
                                        )}
                                    </div>
                                </div>
                                {item.hasArrow && (
                                    <ArrowRight size={16} className="text-gray-400 group-hover:text-gray-600" />
                                )}
                            </button>
                        ))}
                    </div>

                    {/* Disconnect Button */}
                    <div className="p-4 border-t border-gray-100">
                        <button
                            className="w-full flex items-center space-x-3 px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                            onClick={() => {
                                logout();
                                disconnectWallet();
                            }}
                        >
                            <LogOut size={18} />
                            <span className="text-sm font-medium">Disconnect Wallet</span>
                        </button>
                    </div>
                </div>
            )}
            {currentStep === 'receive' && (
                <QRReceiveModal
                    isOpen={showReceiveModal}
                    onClose={() => { setShowReceiveModal(false); setCurrentStep('wallet'); setHeader({ backButton: false, title: '' }) }}
                    address={address}
                    network={network}
                />
            )}
        </>
    );
};