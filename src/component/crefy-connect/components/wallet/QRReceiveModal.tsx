import React, { useState, useEffect, useRef } from 'react';
import { Co<PERSON>, ArrowLeft } from 'lucide-react';
import { AuthFooter } from '../AuthFooter';

interface QRReceiveModalProps {
    isOpen: boolean;
    onClose: () => void;
    address: string;
    network: string;
}

declare class QRCode {
    constructor(element: HTMLElement, config: {
        text: string;
        width: number;
        height: number;
        colorDark: string;
        colorLight: string;
        correctLevel: number;
    });
}

export const QRReceiveModal: React.FC<QRReceiveModalProps> = ({
    isOpen,
    onClose,
    address,
    network
}) => {
    const qrRef = useRef<HTMLDivElement>(null);
    const [copied, setCopied] = useState(false);
    const qrCodeInstance = useRef<QRCode | null>(null);
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);

    useEffect(() => {
        if (!isOpen || !qrRef.current) return;

        const loadQRCode = async () => {
            try {
                if (!(window as any).QRCode) {
                    await new Promise((resolve, reject) => {
                        const script = document.createElement('script');
                        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js';
                        script.onload = resolve;
                        script.onerror = reject;
                        document.head.appendChild(script);
                    });
                }

                // Clear the container first
                while (qrRef.current?.firstChild) {
                    qrRef.current.removeChild(qrRef.current.firstChild);
                }

                if ((window as any).QRCode) {
                    qrCodeInstance.current = new (window as any).QRCode(qrRef.current, {
                        text: address,
                        width: 300,
                        height: 300,
                        colorDark: '#000000',
                        colorLight: '#ffffff',
                        correctLevel: (window as any).QRCode.CorrectLevel.H
                    });
                }
            } catch (error) {
                console.error('Failed to load QR code library:', error);
            }
        };

        loadQRCode();

        return () => {
            // Clean up by removing all children
            if (qrRef.current) {
                while (qrRef.current.firstChild) {
                    qrRef.current.removeChild(qrRef.current.firstChild);
                }
            }
            qrCodeInstance.current = null;

            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, [isOpen, address]);

    const handleCopy = async () => {
        try {
            await navigator.clipboard.writeText(address);
            setCopied(true);

            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }

            timeoutRef.current = setTimeout(() => {
                setCopied(false);
            }, 2000);
        } catch (err) {
            console.error('Failed to copy address: ', err);
            // Fallback for browsers that don't support clipboard API
            const textArea = document.createElement('textarea');
            textArea.value = address;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                setCopied(true);
                setTimeout(() => setCopied(false), 2000);
            } catch (copyErr) {
                console.error('Fallback copy failed: ', copyErr);
            }
            document.body.removeChild(textArea);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="bg-white rounded-2xl">
            {/* Copied notification */}
            {copied && (
                <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-black text-white px-4 py-2 rounded-md z-10">
                    Copied!
                </div>
            )}

            <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors absolute top-4 left-4 text-gray-500"
                aria-label="Close modal"
            >
                <ArrowLeft size={20} className="text-gray-500" />
            </button>

            <div className="p-6">
                <div className="flex flex-col items-center mb-6">
                    <p className="text-gray-500 text-sm mb-4">Scan this QR code with a wallet app</p>

                    <div className="p-4 bg-white rounded-2xl shadow-lg border border-gray-100">
                        <div
                            ref={qrRef}
                            className="flex items-center justify-center min-h-[250px] min-w-[250px] overflow-hidden"
                            aria-label={`QR code for ${network} address`}
                        >
                        </div>
                    </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                        <code className="text-sm font-mono text-gray-900 break-all">{address}</code>
                        <button
                            onClick={handleCopy}
                            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                            aria-label="Copy address"
                        >
                            <Copy size={16} className="text-gray-500" />
                        </button>
                    </div>
                </div>
            </div>
            <AuthFooter />
        </div>
    );
};